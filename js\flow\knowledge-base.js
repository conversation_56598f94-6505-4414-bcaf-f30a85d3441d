/**
 * ============================================================================
 * 🚀 核心业务流程 - 知识库管理器 (简化版 - 本地数据)
 * ============================================================================
 *
 * @fileoverview 知识库管理器 - 简化版本
 * @description 专注本地数据管理，移除复杂的API集成和懒加载机制
 *
 * @businessFlow 知识库数据管理 (简化版)
 * 在核心业务流程中的位置：
 * 为地址处理流水线提供本地数据支持：
 * 1. 精简酒店数据查询
 * 2. 基础地址翻译映射
 * 3. 机场代码和名称映射
 * 4. 简化的数据缓存
 *
 * @architecture Simplified Local Data Service
 * - 职责：本地数据查询和基础缓存
 * - 原则：简化依赖，专注本地处理
 * - 接口：为Gemini处理提供数据上下文
 *
 * @dependencies 依赖关系
 * 上游依赖：
 * - flow/order-parser.js (订单解析时查询)
 * - flow/address-translator.js (地址翻译时查询)
 * 下游依赖：
 * - data/hotel-data.json (酒店数据文件)
 * - data/airport-data.json (机场数据文件)
 *
 * @localProcessing 本地处理职责（核心功能）
 * - 🟢 酒店知识库加载和管理
 * - 🟢 机场代码和名称映射
 * - 🟢 地址模糊匹配和搜索
 * - 🟢 数据缓存和性能优化
 * - 🟢 知识库更新和同步
 *
 * @remoteProcessing 远程处理职责
 * - ❌ 无（纯数据管理模块）
 *
 * @compatibility 兼容性保证
 * - 保持现有酒店数据格式
 * - 兼容现有的查询接口
 * - 保持数据结构不变
 *
 * @refactoringConstraints 重构约束
 * - ✅ 不能调用远程API（严格本地数据）
 * - ✅ 不能依赖其他子层
 * - ✅ 必须保持数据查询性能
 * - ✅ 保持现有的数据格式
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-08-09
 * @lastModified 2025-08-09
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};

(function() {
    'use strict';

    /**
     * 知识库管理器 - 子层实现
     */
    class KnowledgeBase {
        constructor() {
            this.logger = this.getLogger();
            
            // 知识库状态
            this.state = {
                hotelKnowledgeBase: {
                    loaded: false,
                    chineseToEnglishMap: new Map(),
                    fuzzySearchIndex: new Map(),
                    totalHotels: 0,
                    loadError: null
                },
                airportData: {
                    loaded: false,
                    codeToNameMap: new Map(),
                    nameToCodeMap: new Map(),
                    totalAirports: 0
                }
            };

            // 初始化知识库
            this.initializeKnowledgeBase();
            
            this.logger.log('知识库管理器已初始化', 'info');
        }

        /**
         * 初始化知识库
         */
        async initializeKnowledgeBase() {
            try {
                this.logger.log('开始初始化知识库...', 'info');

                // 并行加载酒店和机场数据
                await Promise.allSettled([
                    this.loadHotelKnowledgeBase(),
                    this.loadAirportData()
                ]);

                this.logger.log('知识库初始化完成', 'success', {
                    hotels: this.state.hotelKnowledgeBase.totalHotels,
                    airports: this.state.airportData.totalAirports
                });

            } catch (error) {
                this.logger.log('知识库初始化失败', 'error', { error: error.message });
            }
        }

        /**
         * 加载酒店知识库（简化版 - 仅本地数据）
         */
        async loadHotelKnowledgeBase() {
            try {
                this.logger.log('开始加载酒店知识库 (简化版)...', 'info');

                // 🚀 简化：仅使用精简启动数据
                if (window.essentialHotelData && window.essentialHotelData.loaded) {
                    this.processHotelData(window.essentialHotelData.data);
                    this.state.hotelKnowledgeBase.loaded = true;
                    this.state.hotelKnowledgeBase.source = 'essential_inline_data';
                    this.logger.log('使用精简酒店数据', 'success', {
                        totalHotels: window.essentialHotelData.totalHotels,
                        source: window.essentialHotelData.source,
                        architecture: 'simplified_local_only'
                    });
                    return;
                }

                // 降级：使用完整内联酒店数据（如果已加载）
                if (window.completeHotelData && window.completeHotelData.loaded) {
                    this.processHotelData(window.completeHotelData.data);
                    this.state.hotelKnowledgeBase.loaded = true;
                    this.state.hotelKnowledgeBase.source = 'complete_inline_data';
                    this.logger.log('使用完整内联酒店数据', 'success', {
                        totalHotels: window.completeHotelData.totalHotels,
                        source: window.completeHotelData.source
                    });
                    return;
                }

                // 降级：使用旧版精简内联酒店数据
                if (window.inlineHotelData && window.inlineHotelData.loaded) {
                    this.processHotelData(window.inlineHotelData.data);
                    this.state.hotelKnowledgeBase.loaded = true;
                    this.state.hotelKnowledgeBase.source = 'basic_inline_data';
                    this.logger.log('使用旧版精简内联酒店数据', 'success');
                    return;
                }

                // 尝试加载外部酒店数据文件 (保持兼容性，但会因CORS失败)
                try {
                    const response = await fetch('data/hotel-data.json');
                    if (response.ok) {
                        const hotelData = await response.json();
                        this.processHotelData(hotelData);
                        this.state.hotelKnowledgeBase.loaded = true;
                        this.state.hotelKnowledgeBase.source = 'external_file';
                        this.logger.log('酒店数据文件加载成功', 'success');
                    } else {
                        throw new Error(`HTTP ${response.status}`);
                    }
                } catch (fetchError) {
                    this.logger.log('酒店数据文件加载失败，使用默认数据', 'warning', {
                        error: fetchError.message,
                        note: 'CORS错误是预期的，系统将使用默认数据'
                    });
                    this.loadDefaultHotelData();
                }

            } catch (error) {
                this.state.hotelKnowledgeBase.loadError = error.message;
                this.logger.log('酒店知识库加载失败', 'error', { error: error.message });
            }
        }









        /**
         * 处理酒店数据
         * @param {array} hotelData - 酒店数据数组
         */
        processHotelData(hotelData) {
            if (!Array.isArray(hotelData)) {
                throw new Error('酒店数据格式不正确');
            }

            const { chineseToEnglishMap, fuzzySearchIndex } = this.state.hotelKnowledgeBase;

            hotelData.forEach(hotel => {
                if (hotel.chinese && hotel.english) {
                    // 建立中英文映射
                    chineseToEnglishMap.set(hotel.chinese, hotel.english);
                    
                    // 建立模糊搜索索引
                    const keywords = [
                        hotel.chinese,
                        hotel.english,
                        ...(hotel.aliases || [])
                    ];
                    
                    keywords.forEach(keyword => {
                        if (keyword) {
                            fuzzySearchIndex.set(keyword.toLowerCase(), hotel);
                        }
                    });
                }
            });

            this.state.hotelKnowledgeBase.totalHotels = hotelData.length;
            this.logger.log('酒店数据处理完成', 'success', { 
                totalHotels: hotelData.length,
                mappings: chineseToEnglishMap.size 
            });
        }

        /**
         * 加载默认酒店数据
         */
        loadDefaultHotelData() {
            const defaultHotels = [
                { chinese: '双子塔', english: 'Petronas Twin Towers' },
                { chinese: '吉隆坡国际机场', english: 'Kuala Lumpur International Airport' },
                { chinese: '市中心', english: 'City Center' },
                { chinese: '武吉免登', english: 'Bukit Bintang' }
            ];

            this.processHotelData(defaultHotels);
            this.state.hotelKnowledgeBase.loaded = true;
            this.logger.log('默认酒店数据加载完成', 'info');
        }

        /**
         * 加载机场数据
         */
        async loadAirportData() {
            try {
                // 默认机场数据
                const defaultAirports = [
                    { code: 'KUL', name: 'Kuala Lumpur International Airport', chinese: '吉隆坡国际机场' },
                    { code: 'SZB', name: 'Sultan Abdul Aziz Shah Airport', chinese: '梳邦机场' },
                    { code: 'JHB', name: 'Senai International Airport', chinese: '新山机场' }
                ];

                const { codeToNameMap, nameToCodeMap } = this.state.airportData;

                defaultAirports.forEach(airport => {
                    codeToNameMap.set(airport.code, airport);
                    nameToCodeMap.set(airport.name.toLowerCase(), airport);
                    if (airport.chinese) {
                        nameToCodeMap.set(airport.chinese, airport);
                    }
                });

                this.state.airportData.totalAirports = defaultAirports.length;
                this.state.airportData.loaded = true;

                this.logger.log('机场数据加载完成', 'success', { 
                    totalAirports: defaultAirports.length 
                });

            } catch (error) {
                this.logger.log('机场数据加载失败', 'error', { error: error.message });
            }
        }

        /**
         * 查询酒店信息 (简化版 - 仅本地数据)
         * @param {string} query - 查询字符串
         * @returns {object|null} 酒店信息
         */
        queryHotel(query) {
            if (!query || typeof query !== 'string') {
                return null;
            }

            // 🚀 简化：优先使用精简启动数据
            if (window.essentialHotelData && window.essentialHotelData.loaded) {
                const result = window.essentialHotelData.queryHotel(query);
                if (result) {
                    return {
                        chinese: result.chinese,
                        english: result.english,
                        region: result.region,
                        priority: result.priority,
                        matchType: 'essential_data_match',
                        source: 'essential_inline_data',
                        confidence: 0.90
                    };
                }
            }

            // 降级：使用完整内联数据（如果已加载）
            if (window.completeHotelData && window.completeHotelData.loaded) {
                const result = window.completeHotelData.queryHotel(query);
                if (result) {
                    return {
                        chinese: result.chinese,
                        english: result.english,
                        region: result.region,
                        matchType: 'complete_data_match',
                        source: 'complete_inline_data',
                        confidence: 0.95
                    };
                }
            }

            // 降级：使用旧版精简内联数据
            if (window.inlineHotelData && window.inlineHotelData.loaded) {
                const inlineManager = window.inlineHotelData.manager;
                if (inlineManager && typeof inlineManager.queryHotel === 'function') {
                    const result = inlineManager.queryHotel(query);
                    if (result) {
                        return {
                            chinese: query,
                            english: result.english,
                            region: result.region,
                            matchType: 'basic_data_match',
                            source: 'basic_inline_data',
                            confidence: 0.85
                        };
                    }
                }
            }

            // 降级：使用本地索引
            const { chineseToEnglishMap, fuzzySearchIndex } = this.state.hotelKnowledgeBase;

            // 精确匹配
            if (chineseToEnglishMap.has(query)) {
                return {
                    chinese: query,
                    english: chineseToEnglishMap.get(query),
                    matchType: 'exact',
                    source: 'local_index',
                    confidence: 0.9
                };
            }

            // 模糊匹配
            const lowerQuery = query.toLowerCase();
            if (fuzzySearchIndex.has(lowerQuery)) {
                const hotel = fuzzySearchIndex.get(lowerQuery);
                return {
                    ...hotel,
                    matchType: 'fuzzy',
                    source: 'local_index',
                    confidence: 0.7
                };
            }

            return null;
        }

        /**
         * 查询机场信息
         * @param {string} query - 查询字符串
         * @returns {object|null} 机场信息
         */
        queryAirport(query) {
            if (!query || typeof query !== 'string') {
                return null;
            }

            const { codeToNameMap, nameToCodeMap } = this.state.airportData;

            // 按代码查询
            if (codeToNameMap.has(query.toUpperCase())) {
                return codeToNameMap.get(query.toUpperCase());
            }

            // 按名称查询
            if (nameToCodeMap.has(query.toLowerCase())) {
                return nameToCodeMap.get(query.toLowerCase());
            }

            // 按中文名称查询
            if (nameToCodeMap.has(query)) {
                return nameToCodeMap.get(query);
            }

            return null;
        }

        /**
         * 获取知识库状态 (简化版)
         * @returns {object} 知识库状态
         */
        getKnowledgeBaseStatus() {
            return {
                hotel: {
                    loaded: this.state.hotelKnowledgeBase.loaded,
                    totalHotels: this.state.hotelKnowledgeBase.totalHotels,
                    source: this.state.hotelKnowledgeBase.source,
                    error: this.state.hotelKnowledgeBase.loadError
                },
                airport: {
                    loaded: this.state.airportData.loaded,
                    totalAirports: this.state.airportData.totalAirports
                },
                version: '3.0.0', // 🚀 简化版本
                architecture: 'simplified_local_data'
            };
        }

        /**
         * 获取酒店知识库统计信息 (简化版)
         * @returns {object} 详细统计信息
         */
        getHotelKnowledgeBaseStats() {
            const baseStats = {
                totalHotels: this.state.hotelKnowledgeBase.totalHotels,
                source: this.state.hotelKnowledgeBase.source,
                mappings: this.state.hotelKnowledgeBase.chineseToEnglishMap.size,
                fuzzyIndexSize: this.state.hotelKnowledgeBase.fuzzySearchIndex.size
            };

            // 添加外部数据源统计
            if (window.essentialHotelData) {
                baseStats.essentialData = window.essentialHotelData.getStats();
            }

            if (window.completeHotelData) {
                baseStats.completeData = window.completeHotelData.getStats();
            }

            return baseStats;
        }

        /**
         * 获取日志服务
         */
        getLogger() {
            return window.OTA?.logger || window.logger || console;
        }
    }

    // 创建全局实例
    const knowledgeBase = new KnowledgeBase();

    // 导出到全局作用域
    window.KnowledgeBase = KnowledgeBase;
    window.OTA.KnowledgeBase = KnowledgeBase;
    window.OTA.knowledgeBase = knowledgeBase;

    console.log('✅ KnowledgeBase (简化版 - 本地数据) 已加载', {
        version: '3.0.0',
        architecture: 'simplified_local_data',
        features: ['精简数据查询', '基础缓存', '本地映射']
    });

})();
