/**
 * 地址处理流水线协调器 (简化版)
 * 协调本地数据查询和Gemini AI处理两个核心步骤
 * 使用精心设计的提示词实现智能地址标准化
 *
 * <AUTHOR>
 * @created 2025-01-13
 * @version 2.0.0 (简化架构)
 * @MANAGER
 */

(function() {
    'use strict';

    /**
     * 地址处理流水线协调器类
     * 负责协调整个地址处理流程
     * @MANAGER
     */
    class AddressPipelineCoordinator {
        constructor() {
            this.logger = this.getLogger();
            this.initialized = false;
            
            // 服务引用
            this.googleMapsService = null;
            this.addressTranslator = null;
            this.knowledgeBase = null;
            
            // 流水线配置 (简化版)
            this.config = {
                // 两步处理流程配置
                pipeline: {
                    steps: [
                        { name: 'local_mapping', enabled: true, timeout: 2000 },
                        { name: 'gemini_processing', enabled: true, timeout: 8000 }
                    ],

                    // 错误处理配置
                    errorHandling: {
                        continueOnError: true,
                        maxRetries: 1,
                        retryDelay: 1000
                    }
                },

                // 结果合并配置
                resultMerging: {
                    priorityOrder: ['gemini_processing', 'local_mapping'],
                    confidenceThreshold: 0.6,
                    requireMinimumSources: 1
                },
                
                // 缓存配置
                caching: {
                    enabled: true,
                    ttl: 24 * 60 * 60 * 1000, // 24小时
                    maxEntries: 1000
                },

                // 后置处理配置：在使用Gemini结果后再做一次本地复核与适配
                postProcessing: {
                    postGeminiLocalAdaptation: true,
                    // 当本地酒店匹配置信度比当前结果高出该阈值时覆盖
                    overrideThreshold: 0.10
                }
            };
            
            // 处理缓存
            this.processingCache = new Map();
            
            // 性能统计 (简化版)
            this.stats = {
                totalProcessed: 0,
                successfulProcessed: 0,
                cacheHits: 0,
                averageProcessingTime: 0,
                stepStats: {
                    local_mapping: { attempts: 0, successes: 0, avgTime: 0 },
                    gemini_processing: { attempts: 0, successes: 0, avgTime: 0 }
                }
            };
        }

        /**
         * 初始化协调器 (简化版)
         * @INIT
         */
        async initialize() {
            if (this.initialized) return;

            try {
                this.logger.log('🚀 初始化地址处理流水线协调器 (简化版)', 'info');

                // 获取服务引用 (简化版)
                await this.initializeServices();

                // 验证服务可用性
                this.validateServices();

                this.initialized = true;
                this.logger.log('✅ 地址处理流水线协调器初始化完成 (简化版)', 'success');

            } catch (error) {
                this.logger.log('❌ 地址处理流水线协调器初始化失败', 'error', {
                    error: error.message
                });
                throw error;
            }
        }

        /**
         * 初始化服务引用 (简化版)
         * @INIT
         */
        async initializeServices() {
            // 地址翻译器 (现在集成Gemini)
            if (window.OTA?.addressTranslator) {
                this.addressTranslator = window.OTA.addressTranslator;
                this.logger.log('地址翻译器已连接 (Gemini集成)', 'success');
            } else {
                this.logger.log('地址翻译器不可用', 'warn');
            }

            // 知识库 (本地数据)
            if (window.OTA?.knowledgeBase) {
                this.knowledgeBase = window.OTA.knowledgeBase;
                this.logger.log('知识库已连接 (本地数据)', 'success');
            } else {
                this.logger.log('知识库不可用', 'warn');
            }

            // 检查精简酒店数据
            if (window.essentialHotelData?.loaded) {
                this.logger.log('精简酒店数据已加载', 'success');
            } else {
                this.logger.log('精简酒店数据不可用', 'warn');
            }
        }

        /**
         * 验证服务可用性
         * @UTIL
         */
        validateServices() {
            const requiredServices = [];
            
            if (!this.addressTranslator) {
                requiredServices.push('地址翻译器');
            }
            
            if (requiredServices.length > 0) {
                throw new Error(`缺少必需的服务: ${requiredServices.join(', ')}`);
            }
        }

        /**
         * 处理地址 - 主入口方法
         * @param {string|object} addressInput - 地址输入（字符串或包含地址字段的对象）
         * @param {object} options - 处理选项
         * @returns {Promise<object>} 处理结果
         * @SERVICE
         */
        async processAddress(addressInput, options = {}) {
            if (!this.initialized) {
                await this.initialize();
            }

            const startTime = Date.now();
            
            try {
                // 标准化输入
                const normalizedInput = this.normalizeAddressInput(addressInput);
                if (!normalizedInput.success) {
                    return normalizedInput;
                }

                const address = normalizedInput.address;
                this.logger.log('开始处理地址', 'info', { address, options });

                // 检查缓存
                const cacheKey = this.generateCacheKey(address, options);
                const cachedResult = this.getCachedResult(cacheKey);
                if (cachedResult) {
                    this.stats.cacheHits++;
                    this.logger.log('使用缓存结果', 'info');
                    return cachedResult;
                }

                // 执行两步流水线处理 (简化版)
                const pipelineResult = await this.executePipeline(address, options);
                
                // 合并和优化结果
                const finalResult = this.mergeResults(pipelineResult, address, options);

                // 新增：如果主要采用了Gemini标准化地址，则对该地址做一次本地复核与适配
                if (this.config.postProcessing?.postGeminiLocalAdaptation) {
                    try {
                        await this.postGeminiLocalAdaptation(finalResult, options);
                    } catch (e) {
                        this.logger.log('后置本地适配失败', 'warn', { error: e.message });
                    }
                }
                
                // 缓存结果
                if (finalResult.success && this.config.caching.enabled) {
                    this.setCachedResult(cacheKey, finalResult);
                }

                // 更新统计
                const processingTime = Date.now() - startTime;
                this.updateStats(finalResult.success, processingTime);

                this.logger.log('地址处理完成', 'success', { 
                    address,
                    success: finalResult.success,
                    processingTime: `${processingTime}ms`
                });

                return finalResult;

            } catch (error) {
                const processingTime = Date.now() - startTime;
                this.updateStats(false, processingTime);
                
                this.logger.log('地址处理失败', 'error', { 
                    addressInput, 
                    error: error.message,
                    processingTime: `${processingTime}ms`
                });

                return {
                    success: false,
                    error: error.message,
                    originalInput: addressInput,
                    processingTime
                };
            }
        }

        /**
         * 标准化地址输入
         * @param {string|object} input - 输入
         * @returns {object} 标准化结果
         * @UTIL
         */
        normalizeAddressInput(input) {
            if (typeof input === 'string') {
                const trimmed = input.trim();
                if (trimmed.length < 2) {
                    return {
                        success: false,
                        error: '地址太短',
                        originalInput: input
                    };
                }
                return {
                    success: true,
                    address: trimmed,
                    type: 'string'
                };
            }

            if (typeof input === 'object' && input !== null) {
                // 从对象中提取地址字段
                const addressFields = ['address', 'pickup', 'dropoff', 'destination'];
                for (const field of addressFields) {
                    if (input[field] && typeof input[field] === 'string') {
                        return {
                            success: true,
                            address: input[field].trim(),
                            type: 'object',
                            field: field,
                            originalObject: input
                        };
                    }
                }
                
                return {
                    success: false,
                    error: '对象中未找到有效的地址字段',
                    originalInput: input
                };
            }

            return {
                success: false,
                error: '无效的地址输入类型',
                originalInput: input
            };
        }

        /**
         * 执行三步流水线处理
         * @param {string} address - 地址
         * @param {object} options - 选项
         * @returns {Promise<object>} 流水线结果
         * @PIPELINE
         */
        async executePipeline(address, options) {
            const results = {
                local_mapping: null,
                gemini_processing: null
            };

            const enabledSteps = this.config.pipeline.steps.filter(step => step.enabled);

            for (const step of enabledSteps) {
                try {
                    this.logger.log(`执行流水线步骤: ${step.name}`, 'info');
                    
                    const stepStartTime = Date.now();
                    const stepResult = await this.executeStep(step.name, address, options);
                    const stepTime = Date.now() - stepStartTime;
                    
                    results[step.name] = stepResult;
                    
                    // 更新步骤统计
                    this.updateStepStats(step.name, stepResult.success, stepTime);
                    
                    this.logger.log(`步骤 ${step.name} 完成`, stepResult.success ? 'success' : 'warn', {
                        success: stepResult.success,
                        time: `${stepTime}ms`
                    });

                } catch (error) {
                    this.logger.log(`步骤 ${step.name} 失败`, 'error', { error: error.message });
                    
                    results[step.name] = {
                        success: false,
                        error: error.message,
                        step: step.name
                    };
                    
                    // 根据配置决定是否继续
                    if (!this.config.pipeline.errorHandling.continueOnError) {
                        break;
                    }
                }
            }

            return results;
        }

        /**
         * 执行单个流水线步骤
         * @param {string} stepName - 步骤名称
         * @param {string} address - 地址
         * @param {object} options - 选项
         * @returns {Promise<object>} 步骤结果
         * @SERVICE
         */
        async executeStep(stepName, address, options) {
            switch (stepName) {
                case 'local_mapping':
                    return await this.executeLocalMapping(address, options);

                case 'gemini_processing':
                    return await this.executeGeminiProcessing(address, options);

                default:
                    throw new Error(`未知的流水线步骤: ${stepName}`);
            }
        }

        /**
         * 执行本地数据映射步骤
         * @param {string} address - 地址
         * @param {object} options - 选项
         * @returns {Promise<object>} 本地映射结果
         * @SERVICE
         */
        async executeLocalMapping(address, options) {
            try {
                const results = {};

                // 1. 知识库查询
                if (this.knowledgeBase) {
                    // 查询酒店信息
                    const hotelInfo = this.knowledgeBase.queryHotel(address);
                    if (hotelInfo) {
                        results.hotel = hotelInfo;
                    }

                    // 查询机场信息
                    const airportInfo = this.knowledgeBase.queryAirport(address);
                    if (airportInfo) {
                        results.airport = airportInfo;
                    }
                }

                // 2. 地址翻译器的本地翻译
                if (this.addressTranslator) {
                    const translationResult = await this.addressTranslator.translateAddress(address);
                    if (translationResult.translations && Object.keys(translationResult.translations).length > 0) {
                        results.translation = translationResult;
                    }
                }

                // 3. 酒店名标准化（本地数据）
                if (this.addressTranslator && this.addressTranslator.standardizeWithLocalData) {
                    const hotelStandardization = await this.addressTranslator.standardizeWithLocalData(address);
                    if (hotelStandardization.success) {
                        results.hotelStandardization = hotelStandardization;
                    }
                }

                const hasResults = Object.keys(results).length > 0;

                return {
                    success: hasResults,
                    step: 'local_mapping',
                    data: results,
                    confidence: hasResults ? 0.75 : 0.0,
                    source: 'local_data'
                };

            } catch (error) {
                return {
                    success: false,
                    step: 'local_mapping',
                    error: error.message
                };
            }
        }

        /**
         * 执行 Gemini AI 处理步骤
         * @param {string} address - 地址
         * @param {object} options - 选项
         * @returns {Promise<object>} Gemini 处理结果
         * @SERVICE
         */
        async executeGeminiProcessing(address, options) {
            if (!this.addressTranslator) {
                return {
                    success: false,
                    step: 'gemini_processing',
                    error: '地址翻译器不可用'
                };
            }

            try {
                const results = {};

                // 使用Gemini进行地址标准化和酒店名识别
                if (this.addressTranslator.processWithGemini) {
                    const geminiResult = await this.addressTranslator.processWithGemini(address, options);
                    if (geminiResult.success) {
                        results.geminiProcessing = geminiResult;

                        // 如果Gemini识别出酒店信息，添加到结果中
                        if (geminiResult.data?.hotelInfo?.detected) {
                            results.hotelStandardization = {
                                success: true,
                                originalName: geminiResult.data.hotelInfo.originalName,
                                standardizedName: geminiResult.data.hotelInfo.standardizedName,
                                confidence: geminiResult.data.hotelInfo.confidence,
                                source: 'gemini_ai',
                                isOfficial: false // Gemini处理的结果标记为非官方
                            };
                        }
                    }
                }

                const hasResults = Object.keys(results).length > 0;

                return {
                    success: hasResults,
                    step: 'gemini_processing',
                    data: results,
                    confidence: hasResults ? 0.85 : 0.0,
                    source: 'gemini_ai'
                };

            } catch (error) {
                return {
                    success: false,
                    step: 'gemini_processing',
                    error: error.message
                };
            }
        }



        /**
         * 合并流水线结果 (简化版)
         * @param {object} pipelineResults - 流水线结果
         * @param {string} originalAddress - 原始地址
         * @param {object} options - 选项
         * @returns {object} 合并后的最终结果
         * @UTIL
         */
        mergeResults(pipelineResults, originalAddress, options) {
            try {
                const mergedResult = {
                    success: false,
                    originalAddress: originalAddress,
                    processedAddress: originalAddress,
                    sources: [],
                    confidence: 0.0,
                    results: {
                        localMapping: null,
                        geminiProcessing: null,
                        hotelStandardization: null,
                        addressStandardization: null
                    },
                    pipelineResults: pipelineResults
                };

                // 按优先级顺序处理结果
                const priorityOrder = this.config.resultMerging.priorityOrder;
                let bestResult = null;
                let bestConfidence = 0;

                for (const stepName of priorityOrder) {
                    const stepResult = pipelineResults[stepName];
                    if (stepResult && stepResult.success && stepResult.confidence > bestConfidence) {
                        bestResult = stepResult;
                        bestConfidence = stepResult.confidence;
                    }
                }

                // 合并本地映射结果
                this.mergeLocalMappingResults(mergedResult, pipelineResults);

                // 合并Gemini处理结果
                this.mergeGeminiProcessingResults(mergedResult, pipelineResults);

                // 合并酒店标准化结果
                this.mergeHotelStandardizationResults(mergedResult, pipelineResults);

                // 设置最终结果
                if (bestResult) {
                    mergedResult.success = true;
                    mergedResult.confidence = bestConfidence;
                    mergedResult.primarySource = bestResult.source;
                }

                // 收集所有成功的源
                Object.keys(pipelineResults).forEach(stepName => {
                    const stepResult = pipelineResults[stepName];
                    if (stepResult && stepResult.success) {
                        mergedResult.sources.push(stepResult.source);
                    }
                });

                return mergedResult;

            } catch (error) {
                this.logger.log('结果合并失败', 'error', { error: error.message });

                return {
                    success: false,
                    error: error.message,
                    originalAddress: originalAddress,
                    processedAddress: originalAddress,
                    pipelineResults: pipelineResults
                };
            }
        }

        /**
         * 合并本地映射结果
         * @param {object} mergedResult - 合并结果对象
         * @param {object} pipelineResults - 流水线结果
         * @UTIL
         */
        mergeLocalMappingResults(mergedResult, pipelineResults) {
            const localResult = pipelineResults.local_mapping;
            if (localResult && localResult.success && localResult.data) {
                mergedResult.results.localMapping = localResult.data;

                // 如果本地映射找到了酒店信息，设置为候选结果
                if (localResult.data.hotelStandardization) {
                    mergedResult.results.hotelStandardization = localResult.data.hotelStandardization;
                }
            }
        }

        /**
         * 合并Gemini处理结果
         * @param {object} mergedResult - 合并结果对象
         * @param {object} pipelineResults - 流水线结果
         * @UTIL
         */
        mergeGeminiProcessingResults(mergedResult, pipelineResults) {
            const geminiResult = pipelineResults.gemini_processing;
            if (geminiResult && geminiResult.success && geminiResult.data) {
                mergedResult.results.geminiProcessing = geminiResult.data;

                // Gemini的地址标准化结果
                if (geminiResult.data.geminiProcessing?.data?.standardizedAddress) {
                    mergedResult.processedAddress = geminiResult.data.geminiProcessing.data.standardizedAddress;
                    mergedResult.results.addressStandardization = {
                        original: mergedResult.originalAddress,
                        standardized: geminiResult.data.geminiProcessing.data.standardizedAddress,
                        source: 'gemini_ai',
                        confidence: geminiResult.data.geminiProcessing.data.confidence || 0.8
                    };
                }

                // 如果Gemini识别出酒店信息，可能覆盖本地映射结果
                if (geminiResult.data.hotelStandardization) {
                    const geminiHotel = geminiResult.data.hotelStandardization;
                    const localHotel = mergedResult.results.hotelStandardization;

                    // 如果Gemini的置信度更高，或者本地没有结果，使用Gemini的结果
                    if (!localHotel || geminiHotel.confidence > (localHotel.confidence || 0)) {
                        mergedResult.results.hotelStandardization = geminiHotel;
                    }
                }
            }
        }

        /**
         * 合并酒店标准化结果
         * @param {object} mergedResult - 合并结果对象
         * @param {object} pipelineResults - 流水线结果
         * @UTIL
         */
        mergeHotelStandardizationResults(mergedResult, pipelineResults) {
            let bestHotelResult = null;
            let bestConfidence = 0;

            // 从各个步骤收集酒店标准化结果
            Object.values(pipelineResults).forEach(stepResult => {
                if (stepResult && stepResult.success && stepResult.data) {
                    const hotelResults = [
                        stepResult.data.hotelStandardization,
                        stepResult.data.hotelStandardizationKG,
                        stepResult.data.hotelStandardizationTranslation
                    ].filter(Boolean);

                    hotelResults.forEach(hotelResult => {
                        if (hotelResult.success && hotelResult.confidence > bestConfidence) {
                            bestHotelResult = hotelResult;
                            bestConfidence = hotelResult.confidence;
                        }
                    });
                }
            });

            if (bestHotelResult) {
                mergedResult.results.hotelStandardization = bestHotelResult;

                // 更新处理后的地址
                if (bestHotelResult.standardizedName && bestHotelResult.standardizedName !== bestHotelResult.originalName) {
                    mergedResult.processedAddress = mergedResult.originalAddress.replace(
                        bestHotelResult.originalName,
                        bestHotelResult.standardizedName
                    );
                }
            }
        }

        /**
         * 后置：基于Gemini标准化后的地址再进行一次本地复核与适配
         * - 目的：避免 Gemini 将酒店名简化为通用名（如将“Dragon Inn Floating Resort”简化为“Dragon Inn”）
         * - 策略：对 processedAddress 运行本地映射；若本地酒店匹配更可靠（高于阈值），则覆盖当前酒店结果
         * @param {object} mergedResult - 合并后的结果对象（包含 processedAddress 与各步骤结果）
         * @param {object} options - 处理选项
         * @returns {Promise<void>}
         * @UTIL
         */
        async postGeminiLocalAdaptation(mergedResult, options = {}) {
            // 仅在Gemini提供了标准化地址时执行
            const addrStd = mergedResult?.results?.addressStandardization;
            const isGeminiStd = addrStd?.source === 'gemini_ai';
            const standardizedAddress = mergedResult?.processedAddress;

            if (!isGeminiStd || !standardizedAddress) return;

            this.logger.log('开始后置本地适配（基于Gemini标准化地址）', 'info', {
                standardizedAddress
            });

            const localOnStd = await this.executeLocalMapping(standardizedAddress, {
                ...options,
                stage: 'post_gemini'
            });

            // 记录到结果中便于调试与可观测性
            mergedResult.results = mergedResult.results || {};
            mergedResult.results.postLocalAdapt = localOnStd;
            if (localOnStd && localOnStd.success) {
                mergedResult.sources.push('post_local_adapt');
            }

            // 若本地复核得到的酒店匹配更可靠，则覆盖当前酒店标准化结果
            const localHotel = localOnStd?.data?.hotelStandardization;
            const currentHotel = mergedResult?.results?.hotelStandardization;
            if (localHotel && localHotel.success) {
                const localC = Number(localHotel.confidence || 0);
                const currentC = Number((currentHotel && currentHotel.confidence) || 0);
                const threshold = Number(this.config.postProcessing?.overrideThreshold || 0);

                if (!currentHotel || localC >= currentC + threshold) {
                    mergedResult.results.hotelStandardization = localHotel;
                    this.logger.log('本地复核覆盖/确认酒店匹配结果', 'info', {
                        used: 'local_override',
                        localConfidence: localC,
                        previousConfidence: currentC,
                        threshold
                    });
                }
            }
        }



        /**
         * 酒店名标准化 - 流水线入口方法
         * @param {string} hotelName - 酒店名
         * @param {object} options - 选项
         * @returns {Promise<object>} 标准化结果
         * @SERVICE
         */
        async standardizeHotelName(hotelName, options = {}) {
            if (!this.initialized) {
                await this.initialize();
            }

            try {
                this.logger.log('开始酒店名标准化流水线', 'info', { hotelName });

                // 使用地址翻译器的标准化功能
                if (this.addressTranslator && this.addressTranslator.standardizeHotelName) {
                    return await this.addressTranslator.standardizeHotelName(hotelName, options);
                }

                // 降级处理：直接使用流水线处理
                const pipelineResult = await this.processAddress(hotelName, {
                    ...options,
                    focusOnHotel: true
                });

                if (pipelineResult.success && pipelineResult.results.hotelStandardization) {
                    return pipelineResult.results.hotelStandardization;
                }

                return {
                    success: false,
                    error: '酒店名标准化失败',
                    originalName: hotelName,
                    standardizedName: hotelName
                };

            } catch (error) {
                this.logger.log('酒店名标准化流水线失败', 'error', {
                    hotelName,
                    error: error.message
                });

                return {
                    success: false,
                    error: error.message,
                    originalName: hotelName,
                    standardizedName: hotelName
                };
            }
        }

        /**
         * 获取日志服务
         * @UTIL
         */
        getLogger() {
            return window.OTA?.logger || window.logger || console;
        }

        /**
         * 生成缓存键
         * @param {string} address - 地址
         * @param {object} options - 选项
         * @returns {string} 缓存键
         * @UTIL
         */
        generateCacheKey(address, options) {
            const optionsStr = JSON.stringify(options);
            return `pipeline_${address}_${optionsStr}`;
        }

        /**
         * 获取缓存结果
         * @param {string} cacheKey - 缓存键
         * @returns {object|null} 缓存结果
         * @UTIL
         */
        getCachedResult(cacheKey) {
            if (!this.config.caching.enabled) return null;
            
            const cached = this.processingCache.get(cacheKey);
            if (cached && Date.now() - cached.timestamp < this.config.caching.ttl) {
                return cached.result;
            }
            return null;
        }

        /**
         * 设置缓存结果
         * @param {string} cacheKey - 缓存键
         * @param {object} result - 结果
         * @UTIL
         */
        setCachedResult(cacheKey, result) {
            if (!this.config.caching.enabled) return;
            
            // 检查缓存大小限制
            if (this.processingCache.size >= this.config.caching.maxEntries) {
                // 删除最旧的条目
                const firstKey = this.processingCache.keys().next().value;
                this.processingCache.delete(firstKey);
            }
            
            this.processingCache.set(cacheKey, {
                result,
                timestamp: Date.now()
            });
        }

        /**
         * 更新统计信息
         * @param {boolean} success - 是否成功
         * @param {number} processingTime - 处理时间
         * @UTIL
         */
        updateStats(success, processingTime) {
            this.stats.totalProcessed++;
            if (success) {
                this.stats.successfulProcessed++;
            }
            
            // 更新平均处理时间
            const totalTime = this.stats.averageProcessingTime * (this.stats.totalProcessed - 1) + processingTime;
            this.stats.averageProcessingTime = totalTime / this.stats.totalProcessed;
        }

        /**
         * 更新步骤统计
         * @param {string} stepName - 步骤名称
         * @param {boolean} success - 是否成功
         * @param {number} stepTime - 步骤时间
         * @UTIL
         */
        updateStepStats(stepName, success, stepTime) {
            const stepStats = this.stats.stepStats[stepName];
            if (stepStats) {
                stepStats.attempts++;
                if (success) {
                    stepStats.successes++;
                }
                
                // 更新平均时间
                const totalTime = stepStats.avgTime * (stepStats.attempts - 1) + stepTime;
                stepStats.avgTime = totalTime / stepStats.attempts;
            }
        }

        /**
         * 获取统计信息
         * @returns {object} 统计信息
         * @UTIL
         */
        getStats() {
            return {
                ...this.stats,
                successRate: this.stats.totalProcessed > 0 
                    ? this.stats.successfulProcessed / this.stats.totalProcessed 
                    : 0,
                cacheHitRate: this.stats.totalProcessed > 0 
                    ? this.stats.cacheHits / this.stats.totalProcessed 
                    : 0,
                cacheSize: this.processingCache.size
            };
        }
    }

    // ===========================================
    // 🌍 全局注册协调器
    // ===========================================
    
    // 创建协调器实例
    const addressPipelineCoordinator = new AddressPipelineCoordinator();
    
    // 注册到全局作用域
    window.AddressPipelineCoordinator = AddressPipelineCoordinator;
    window.addressPipelineCoordinator = addressPipelineCoordinator;
    
    // OTA 命名空间注册
    window.OTA = window.OTA || {};
    window.OTA.AddressPipelineCoordinator = AddressPipelineCoordinator;
    window.OTA.addressPipelineCoordinator = addressPipelineCoordinator;

    console.log('✅ 地址处理流水线协调器已加载 (简化版)', {
        version: '2.0.0',
        architecture: 'Gemini + 本地数据库',
        features: ['两步流水线处理', 'Gemini AI集成', '本地数据映射', '结果合并'],
        ready: false // 需要调用 initialize() 初始化
    });

})();
