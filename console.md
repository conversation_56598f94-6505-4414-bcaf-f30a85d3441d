---
type: "manual"
---

 ✅ Script manifest ready
 ✅ ScriptLoader ready
 🔧 Loading phase: core (15 scripts)
 ✅ 依赖容器已初始化
 ✅ 服务定位器已初始化
 ✅ 服务定位器已加载
 ✅ 应用启动协调器已加载
 [3:05:10 PM] [INFO] 🔍 初始化基础监控系统... {type: 'monitoring_init'}
 [3:05:10 PM] [INFO] 全局错误处理已设置 {type: 'global_error_handler_init'}
 [3:05:10 PM] [SUCCESS] ✅ 基础监控系统初始化完成 {type: 'monitoring_ready'}
 [3:05:10 PM] [INFO] OTA订单处理系统启动 {type: 'system_start', userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb…KHTML, like Gecko) Chrome/********* Safari/537.36', timestamp: '2025-08-13T07:05:10.683Z'}
 [3:05:10 PM] [INFO] [DependencyContainer] 已注册服务: formManagerClass 
 [DependencyContainer] 已注册服务: formManagerClass
 [3:05:10 PM] [INFO] [FormManager] ✅ FormManager类已注册到依赖容器 
 [FormManager] ✅ FormManager类已注册到依赖容器
 [3:05:10 PM] [INFO] ✅ 表单管理器类已加载（关键路径优化） 
 ✅ 表单管理器类已加载（关键路径优化）
 [3:05:10 PM] [INFO] ✅ Google Maps API 配置已加载 [object Object] 
 ✅ Google Maps API 配置已加载 {version: '1.0.0', features: Array(5), cacheEnabled: true}
 [3:05:10 PM] [INFO] [DependencyContainer] 已注册服务: eventCoordinator 
 [DependencyContainer] 已注册服务: eventCoordinator
 [3:05:10 PM] [INFO] [EventCoordinator] 已注册组件: vehicleConfigManager {options: {…}}
 [3:05:10 PM] [INFO] [DependencyContainer] 已注册服务: vehicleConfigManager 
 [DependencyContainer] 已注册服务: vehicleConfigManager
 [3:05:10 PM] [DEBUG] [VehicleConfigManager] 已注册到依赖容器 
 [3:05:10 PM] [INFO] [VehicleConfigManager] 车辆配置管理器已初始化 {defaultCarTypeId: 5, recommendationRules: 8}
 [3:05:10 PM] [INFO] ✅ 车辆配置管理器已加载 
 ✅ 车辆配置管理器已加载
 [3:05:10 PM] [INFO] [GlobalFieldStandardization] ✅ 全局字段标准化拦截层已加载并设置自动初始化 
 [GlobalFieldStandardization] ✅ 全局字段标准化拦截层已加载并设置自动初始化
 [3:05:10 PM] [INFO] 特性开关管理器已初始化 
 [3:05:10 PM] [INFO] ✅ 特性开关机制已加载 
 ✅ 特性开关机制已加载
 [3:05:10 PM] [INFO] ✅ 精简酒店数据已加载 [object Object] 
 ✅ 精简酒店数据已加载 {version: '1.0.0', totalHotels: 57, source: 'essential_inline_data', optimizedFor: 'startup_performance', sizeReduction: '90%', …}
 [3:05:10 PM] [INFO] ✅ Phase complete: core in 109.1ms 
 ✅ Phase complete: core in 109.1ms
 [3:05:10 PM] [INFO] 🔧 Loading phase: ota-architecture (20 scripts) 
 🔧 Loading phase: ota-architecture (20 scripts)
 [3:05:10 PM] [INFO] ✅ BaseManager适配器已加载，BaseManager全局类已可用 
 ✅ BaseManager适配器已加载，BaseManager全局类已可用
 [3:05:10 PM] [INFO] ✅ 统一版OTA管理器已加载 
 ✅ 统一版OTA管理器已加载
 [3:05:10 PM] [INFO] ✅ 统一OTA策略配置已加载 
 ✅ 统一OTA策略配置已加载
 [3:05:10 PM] [INFO] ✅ Google Maps 服务模块已加载 [object Object] 
 ✅ Google Maps 服务模块已加载 {version: '1.0.0', features: Array(5), ready: false}
 [3:05:10 PM] [INFO] ✅ ChannelDetector (子层实现) 已加载 
 ✅ ChannelDetector (子层实现) 已加载
 [3:05:10 PM] [INFO] 提示词构建器已初始化 
 [3:05:10 PM] [INFO] ✅ PromptBuilder (子层实现) 已加载 
 ✅ PromptBuilder (子层实现) 已加载
 [3:05:10 PM] [INFO] Gemini API调用器已初始化 {model: 'gemini-2.5-flash', baseTimeout: 25000, version: '3.0', timestamp: '2025-08-13T07:05:10.775Z'}
 [3:05:10 PM] [INFO] ✅ GeminiCaller (子层实现) 已加载 
 ✅ GeminiCaller (子层实现) 已加载
 [3:05:10 PM] [INFO] 结果处理器已初始化 
 [3:05:10 PM] [INFO] ✅ ResultProcessor (子层实现) 已加载 
 ✅ ResultProcessor (子层实现) 已加载
 [3:05:10 PM] [INFO] 订单解析器已初始化 
 [3:05:10 PM] [INFO] ✅ OrderParser (子层实现) 已加载 
 ✅ OrderParser (子层实现) 已加载
 [3:05:10 PM] [INFO] 开始初始化知识库... 
 [3:05:10 PM] [INFO] 开始加载酒店知识库... 
 [3:05:10 PM] [SUCCESS] 酒店数据处理完成 {totalHotels: 57, mappings: 57}
 [3:05:10 PM] [SUCCESS] 使用精简启动酒店数据 {totalHotels: 57, source: 'essential_inline_data', optimizedFor: 'startup_performance'}
 [3:05:10 PM] [INFO] 已调度完整酒店数据的懒加载 
 [3:05:10 PM] [SUCCESS] 机场数据加载完成 {totalAirports: 3}
 [3:05:10 PM] [INFO] 知识库管理器已初始化 
 [3:05:10 PM] [INFO] ✅ KnowledgeBase (子层实现) 已加载 
 ✅ KnowledgeBase (子层实现) 已加载
 [3:05:10 PM] [SUCCESS] 知识库初始化完成 {hotels: 57, airports: 3}
 [3:05:10 PM] [INFO] 地址翻译器已初始化 
 [3:05:10 PM] [SUCCESS] ✅ Google Maps 服务初始化完成 
 [3:05:10 PM] [INFO] ✅ AddressTranslator (子层实现) 已加载 
 ✅ AddressTranslator (子层实现) 已加载
 [3:05:10 PM] [SUCCESS] Google Maps 服务已连接 
 [3:05:10 PM] [INFO] [GlobalFieldStandardization] 🔧 特性开关已启用，开始自动初始化... 
 [GlobalFieldStandardization] 🔧 特性开关已启用，开始自动初始化...
 [3:05:10 PM] [INFO] [GlobalFieldStandardization] 尝试安装Gemini拦截器 (1/150) [object Object] 
 [GlobalFieldStandardization] 尝试安装Gemini拦截器 (1/150) {hasAdapter: false, hasCaller: true, adapterHasParseOrder: false, adapterHasLegacyParse: false, callerHasParseAPIResponse: true}
 [3:05:10 PM] [INFO] [GlobalFieldStandardization] ✅ GeminiCaller拦截器已安装 (parseAPIResponse) 
 [GlobalFieldStandardization] ✅ GeminiCaller拦截器已安装 (parseAPIResponse)
 [3:05:10 PM] [INFO] [GlobalFieldStandardization] ⚠️ 表单管理器服务未在依赖容器中注册，跳过拦截 
 [GlobalFieldStandardization] ⚠️ 表单管理器服务未在依赖容器中注册，跳过拦截
 [3:05:10 PM] [INFO] [GlobalFieldStandardization] ℹ️ 跳过实时分析管理器拦截器（减法修复） 
 [GlobalFieldStandardization] ℹ️ 跳过实时分析管理器拦截器（减法修复）
 [3:05:10 PM] [INFO] [GlobalFieldStandardization] ✅ 字段标准化拦截层初始化完成 
 [GlobalFieldStandardization] ✅ 字段标准化拦截层初始化完成
 [3:05:10 PM] [INFO] 🌐 开始初始化统一语言检测器... 
 🌐 开始初始化统一语言检测器...
 [3:05:10 PM] [INFO] 🌐 初始化统一语言检测器... 
 [3:05:10 PM] [INFO] ✅ 已绑定字段事件: #customerName 
 [3:05:10 PM] [INFO] ✅ 已绑定字段事件: #pickup 
 [3:05:10 PM] [INFO] ✅ 已绑定字段事件: #dropoff 
 [3:05:10 PM] [INFO] ✅ 已绑定字段事件: #extraRequirement 
 [3:05:10 PM] [INFO] ✅ 已绑定字段事件: #flightInfo 
 [3:05:10 PM] [INFO] 📝 已绑定 5 个字段的语言检测事件 
 [3:05:10 PM] [INFO] ✅ 已设置默认语言（英文） 
 [3:05:10 PM] [SUCCESS] ✅ 统一语言检测器初始化完成 
 [3:05:10 PM] [INFO] ✅ 统一语言检测器初始化成功 
 ✅ 统一语言检测器初始化成功
 [3:05:10 PM] [INFO] ✅ 地址处理流水线协调器已加载 [object Object] 
 ✅ 地址处理流水线协调器已加载 {version: '1.0.0', features: Array(4), ready: false}
 [3:05:10 PM] [INFO] 多订单处理器已初始化 
 [3:05:10 PM] [INFO] ✅ MultiOrderHandler (子层实现) 已加载 
 ✅ MultiOrderHandler (子层实现) 已加载
 [3:05:10 PM] [INFO] 开始后台加载完整酒店数据... 
 [3:05:10 PM] [INFO] 开始动态加载完整酒店数据脚本 
 [3:05:10 PM] [INFO] API调用器已初始化 
 [3:05:10 PM] [INFO] ✅ APICaller (子层实现) 已加载 
 ✅ APICaller (子层实现) 已加载
 [3:05:10 PM] [INFO] ✅ 完整酒店数据模块已加载 
 ✅ 完整酒店数据模块已加载
 [3:05:10 PM] [INFO] ✅ 完整酒店数据初始化完成，共 87 家酒店 
 ✅ 完整酒店数据初始化完成，共 87 家酒店
 [3:05:10 PM] [INFO] 📊 数据统计: 87 家酒店，15 个区域 
 📊 数据统计: 87 家酒店，15 个区域
 [3:05:10 PM] [SUCCESS] 完整酒店数据脚本加载成功 
 [3:05:10 PM] [SUCCESS] 历史数据加载成功 {}
 [3:05:10 PM] [SUCCESS] 历史管理器初始化完成 {historyCount: 0}
 [3:05:10 PM] [INFO] 历史管理器已初始化 
 [3:05:10 PM] [INFO] ✅ HistoryManager (子层实现) 已加载 
 ✅ HistoryManager (子层实现) 已加载
 [3:05:10 PM] [INFO] 渠道检测器已初始化 
 [3:05:10 PM] [INFO] 业务流程控制器已初始化 
 [3:05:10 PM] [INFO] ✅ BusinessFlowController (母层控制器) 已加载 
 ✅ BusinessFlowController (母层控制器) 已加载
 [3:05:10 PM] [INFO] 订单管理控制器已初始化 
 [3:05:10 PM] [INFO] ✅ OrderManagementController (母层控制器) 已加载 
 ✅ OrderManagementController (母层控制器) 已加载
 [3:05:10 PM] [INFO] Gemini服务适配器已初始化 
 [3:05:10 PM] [INFO] ✅ GeminiServiceAdapter (兼容性适配器) 已加载 
 ✅ GeminiServiceAdapter (兼容性适配器) 已加载
 [3:05:10 PM] [INFO] [GlobalFieldStandardization] ✅ 多订单管理器拦截器已安装 
 [GlobalFieldStandardization] ✅ 多订单管理器拦截器已安装
 [3:05:10 PM] [INFO] 🔄 MultiOrderManagerAdapter 初始化开始 
 [3:05:10 PM] [INFO] ✅ MultiOrderManagerAdapter 已加载并注册 
 ✅ MultiOrderManagerAdapter 已加载并注册
 [3:05:10 PM] [SUCCESS] ✅ MultiOrderManagerAdapter 初始化完成 
 [3:05:10 PM] [INFO] 🔄 UIManagerAdapter 初始化开始 
 [3:05:10 PM] [INFO] ✅ UIManagerAdapter 已加载并注册 
 ✅ UIManagerAdapter 已加载并注册
 [3:05:10 PM] [SUCCESS] ✅ UIManagerAdapter 初始化完成 
 [3:05:10 PM] [INFO] ✅ Phase complete: ota-architecture in 201.6ms 
 ✅ Phase complete: ota-architecture in 201.6ms
 [3:05:10 PM] [INFO] 🔧 Loading phase: services (18 scripts) 
 🔧 Loading phase: services (18 scripts)
 [3:05:10 PM] [SUCCESS] 酒店数据处理完成 {totalHotels: 87, mappings: 87}
 [3:05:10 PM] [SUCCESS] ✅ 已升级到完整酒店数据 {previousHotels: 57, newHotels: 87, improvement: '+30 家酒店', source: 'complete_inline_data'}
 [3:05:11 PM] [INFO] ✅ 内联酒店数据模块已加载 
 ✅ 内联酒店数据模块已加载
 [3:05:11 PM] [INFO] 历史订单关闭按钮事件已绑定 
 [3:05:11 PM] [INFO] 历史订单清空按钮事件已绑定 
 [3:05:11 PM] [INFO] 历史订单导出按钮事件已绑定 
 [3:05:11 PM] [INFO] 历史订单搜索按钮事件已绑定 
 [3:05:11 PM] [INFO] 历史订单重置按钮事件已绑定 
 [3:05:11 PM] [INFO] 历史订单管理器已初始化（按账号存储） 
 [3:05:11 PM] [DEBUG] 部分子层模块未加载，将使用降级方案 
 [3:05:11 PM] [SUCCESS] 新架构组件连接成功 
 [3:05:11 PM] [INFO] [GlobalFieldStandardization] ✅ API服务拦截器已安装 
 [GlobalFieldStandardization] ✅ API服务拦截器已安装
 [3:05:11 PM] [SUCCESS] 图片上传按钮事件已绑定 
 [3:05:11 PM] [INFO] 图片上传管理器已初始化 
 [3:05:11 PM] [INFO] ✈️ 航班信息服务已初始化 {baseUrl: '', timestamp: '2025-08-13T07:05:11.100Z'}
 [3:05:11 PM] [INFO] ✅ 字段映射配置已加载 [object Object] 
 ✅ 字段映射配置已加载 {aiToFrontendMappings: 22, frontendToApiMappings: 28, alternativeFields: 14}
 [3:05:11 PM] [INFO] ✅ 字段映射验证器已加载 
 ✅ 字段映射验证器已加载
 [3:05:11 PM] [INFO] ✅ 多订单检测服务已加载 
 ✅ 多订单检测服务已加载
 [3:05:11 PM] [INFO] ✅ 多订单UI渲染器已加载 
 ✅ 多订单UI渲染器已加载
 [3:05:11 PM] [INFO] ✅ 多订单批量处理器已加载 
 ✅ 多订单批量处理器已加载
 [3:05:11 PM] [INFO] ✅ 多订单数据转换器已加载 
 ✅ 多订单数据转换器已加载
 [3:05:11 PM] [INFO] 状态已从本地存储加载 
 [3:05:11 PM] [INFO] 多订单状态管理器初始化完成 
 [3:05:11 PM] [INFO] 多订单协调器模块初始化完成 
 [3:05:11 PM] [INFO] ✅ 系统完整性检查器已加载 
 ✅ 系统完整性检查器已加载
 [3:05:11 PM] [INFO] ✅ Phase complete: services in 212.8ms 
 ✅ Phase complete: services in 212.8ms
 [3:05:11 PM] [INFO] 🔧 Loading phase: ui-managers (8 scripts) 
 🔧 Loading phase: ui-managers (8 scripts)
 [3:05:11 PM] [INFO] 🔧 自适应高度管理器初始化中... 
 [3:05:11 PM] [INFO] 📏 已添加自适应高度管理: extraRequirement 
 [3:05:11 PM] [INFO] 📏 已添加自适应高度管理: pickup 
 [3:05:11 PM] [INFO] 📏 已添加自适应高度管理: dropoff 
 [3:05:11 PM] [INFO] 📏 已添加自适应高度管理: customerName 
 [3:05:11 PM] [INFO] 📏 已添加自适应高度管理: flightInfo 
 [3:05:11 PM] [INFO] 📏 已添加自适应高度管理: otaReferenceNumber 
 [3:05:11 PM] [INFO] 📏 已添加自适应高度管理: orderInput 
 [3:05:11 PM] [INFO] 📏 已添加自适应高度管理: searchOrderId 
 [3:05:11 PM] [INFO] 📏 已添加自适应高度管理: searchCustomer 
 [3:05:11 PM] [SUCCESS] ✅ 自适应高度管理器初始化完成 
 [3:05:11 PM] [SUCCESS] 权限管理器初始化完成 {version: '2.0.0', enabled: true}
 [3:05:11 PM] [INFO] ✅ 权限管理器已加载 
 ✅ 权限管理器已加载
 [3:05:11 PM] [INFO] 🎬 初始化动画管理器... 
 [3:05:11 PM] [SUCCESS] ✅ 按钮动画处理器初始化完成 
 [3:05:11 PM] [SUCCESS] ✅ 动画管理器初始化完成 {enabled: true, config: {…}}
 [3:05:11 PM] [INFO] ✅ 动画管理器已加载并初始化 
 ✅ 动画管理器已加载并初始化
 [3:05:11 PM] [INFO] ✅ Phase complete: ui-managers in 92.5ms 
 ✅ Phase complete: ui-managers in 92.5ms
 [3:05:11 PM] [INFO] 🔧 Loading phase: ui (2 scripts) 
 🔧 Loading phase: ui (2 scripts)
 [3:05:11 PM] [INFO] 🚀 开始启动OTA订单处理系统... 
 🚀 开始启动OTA订单处理系统...
 [3:05:11 PM] [INFO] 🚀 开始启动OTA订单处理系统... 
 🚀 开始启动OTA订单处理系统...
 [3:05:11 PM] [INFO] 📋 执行启动阶段: dependencies (1/5) 
 📋 执行启动阶段: dependencies (1/5)
 [3:05:11 PM] [INFO] [DependencyContainer] 已注册服务: appState 
 [DependencyContainer] 已注册服务: appState
 [3:05:11 PM] [INFO] [DependencyContainer] 已注册服务: logger 
 [DependencyContainer] 已注册服务: logger
 [3:05:11 PM] [INFO] [DependencyContainer] 已注册服务: utils 
 [DependencyContainer] 已注册服务: utils
 [3:05:11 PM] [INFO] [DependencyContainer] 警告: 服务 eventCoordinator 已存在，将被覆盖 
 [DependencyContainer] 警告: 服务 eventCoordinator 已存在，将被覆盖
 [3:05:11 PM] [INFO] [DependencyContainer] 已注册服务: eventCoordinator 
 [DependencyContainer] 已注册服务: eventCoordinator
 [3:05:11 PM] [INFO] [DependencyContainer] 已注册服务: apiService 
 [DependencyContainer] 已注册服务: apiService
 [3:05:11 PM] [INFO] [DependencyContainer] 已注册服务: geminiService 
 [DependencyContainer] 已注册服务: geminiService
 [3:05:11 PM] [INFO] [DependencyContainer] 已注册服务: i18nManager 
 [DependencyContainer] 已注册服务: i18nManager
 [3:05:11 PM] [INFO] [DependencyContainer] 已注册服务: formManager 
 [DependencyContainer] 已注册服务: formManager
 [3:05:11 PM] [INFO] [DependencyContainer] 已注册服务: imageUploadManager 
 [DependencyContainer] 已注册服务: imageUploadManager
 [3:05:11 PM] [INFO] [DependencyContainer] 已注册服务: currencyConverter 
 [DependencyContainer] 已注册服务: currencyConverter
 [3:05:11 PM] [INFO] [DependencyContainer] 已注册服务: multiOrderManager 
 [DependencyContainer] 已注册服务: multiOrderManager
 [3:05:11 PM] [INFO] [DependencyContainer] 已注册服务: orderHistoryManager 
 [DependencyContainer] 已注册服务: orderHistoryManager
 [3:05:11 PM] [INFO] [DependencyContainer] 已注册服务: pagingServiceManager 
 [DependencyContainer] 已注册服务: pagingServiceManager
 [3:05:11 PM] [INFO] [DependencyContainer] 已注册服务: uiManager 
 [DependencyContainer] 已注册服务: uiManager
 [3:05:11 PM] [INFO] [DependencyContainer] 已注册服务: channelDetector 
 [DependencyContainer] 已注册服务: channelDetector
 [3:05:11 PM] [INFO] 📦 已注册 15 个依赖 
 📦 已注册 15 个依赖
 [3:05:11 PM] [INFO] 📋 执行启动阶段: services (2/5) 
 📋 执行启动阶段: services (2/5)
 [3:05:11 PM] [INFO] [DependencyContainer] 已创建服务实例: appState 
 [DependencyContainer] 已创建服务实例: appState
 [3:05:11 PM] [INFO] [DependencyContainer] 已创建服务实例: logger 
 [3:05:11 PM] [INFO] [DependencyContainer] 已创建服务实例: utils 
 [3:05:11 PM] [INFO] [EventCoordinator] 全局事件监听器已设置 
 [3:05:11 PM] [INFO] [EventCoordinator] 全局事件协调器已初始化 
 [3:05:11 PM] [INFO] [DependencyContainer] 已创建服务实例: eventCoordinator 
 [3:05:11 PM] [DEBUG] [EventCoordinator] 全局事件协调器已经初始化 
 [3:05:11 PM] [INFO] [DependencyContainer] 已创建服务实例: apiService 
 [3:05:11 PM] [INFO] [ApiService] 开始初始化... 
 [3:05:11 PM] [INFO] [ApiService] AppState中无系统数据，使用静态数据初始化... 
 [3:05:11 PM] [SUCCESS] [ApiService] 静态数据同步完成 {languagesCount: 11, backendUsersCount: 43}
 [3:05:11 PM] [SUCCESS] [ApiService] 初始化完成 
 [3:05:11 PM] [INFO] [DependencyContainer] 已创建服务实例: geminiService 
 [3:05:11 PM] [INFO] [DependencyContainer] 已创建服务实例: channelDetector 
 [3:05:11 PM] [INFO] ⚙️ 已初始化 6 个核心服务 
 ⚙️ 已初始化 6 个核心服务
 [3:05:11 PM] [INFO] 📋 执行启动阶段: managers (3/5) 
 📋 执行启动阶段: managers (3/5)
 [3:05:11 PM] [INFO] [DependencyContainer] 已创建服务实例: imageUploadManager 
 [3:05:11 PM] [SUCCESS] 图片上传按钮事件已绑定 
 [3:05:11 PM] [INFO] 图片上传管理器已初始化 
 [3:05:11 PM] [INFO] [DependencyContainer] 已创建服务实例: currencyConverter 
 [3:05:11 PM] [INFO] [DependencyContainer] 已创建服务实例: multiOrderManager 
 [3:05:11 PM] [INFO] [DependencyContainer] 已创建服务实例: orderHistoryManager 
 [3:05:11 PM] [INFO] 历史订单关闭按钮事件已绑定 
 [3:05:11 PM] [INFO] 历史订单清空按钮事件已绑定 
 [3:05:11 PM] [INFO] 历史订单导出按钮事件已绑定 
 [3:05:11 PM] [INFO] 历史订单搜索按钮事件已绑定 
 [3:05:11 PM] [INFO] 历史订单重置按钮事件已绑定 
 [3:05:11 PM] [INFO] 历史订单管理器已初始化（按账号存储） 
 [3:05:11 PM] [INFO] [DependencyContainer] 已创建服务实例: pagingServiceManager 
 [3:05:11 PM] [INFO] 🎛️ 已处理 5 个管理器 
 🎛️ 已处理 5 个管理器
 [3:05:11 PM] [INFO] 📋 执行启动阶段: ui (4/5) 
 📋 执行启动阶段: ui (4/5)
 [3:05:11 PM] [INFO] [DependencyContainer] 已创建服务实例: i18nManager 
 [3:05:11 PM] [INFO] 国际化管理器已初始化，当前语言: zh 
 [3:05:11 PM] [INFO] ✅ Phase complete: ui in 79.0ms 
 ✅ Phase complete: ui in 79.0ms
 [3:05:11 PM] [INFO] 🚀 All scripts loaded in 695.9ms 
 🚀 All scripts loaded in 695.9ms
 [3:05:11 PM] [INFO] [DependencyContainer] 已创建服务实例: uiManager 
 [3:05:11 PM] [INFO] UIManager 开始初始化... 
 [3:05:11 PM] [INFO] DOM元素缓存完成 
 [3:05:11 PM] [INFO] DOM元素已缓存 
 [3:05:11 PM] [INFO] 显示主工作区 
 [3:05:11 PM] [INFO] 系统数据已存在，无需重新初始化 {languagesCount: 11}
 [3:05:11 PM] [SUCCESS] ✅ 表单管理器动画集成成功 
 [3:05:11 PM] [INFO] 价格转换监听器初始化完成（货币切换逻辑由PriceManager处理） 
 [3:05:11 PM] [SUCCESS] 自适应高度输入框初始化完成 
 [3:05:11 PM] [SUCCESS] 表单管理器初始化完成 
logger.js:467 [3:05:11 PM] [WARNING] [DependencyContainer] 警告: 服务 formManager 已存在，将被覆盖 
outputToConsole @ logger.js:467
log @ logger.js:384
log @ dependency-container.js:230
register @ dependency-container.js:47
initializeManagers @ ui-manager.js:270
init @ ui-manager.js:113
initializeUI @ application-bootstrap.js:342
await in initializeUI
executePhase @ application-bootstrap.js:119
start @ application-bootstrap.js:71
await in start
startApp @ main.js:77
(anonymous) @ main.js:123
 [3:05:11 PM] [INFO] [DependencyContainer] 已注册服务: formManager 
 [3:05:11 PM] [INFO] [UIManager] ✅ FormManager实例已注册到依赖容器 
 [UIManager] ✅ FormManager实例已注册到依赖容器
 [3:05:11 PM] [SUCCESS] ✅ UI状态管理器动画集成成功 
 [3:05:11 PM] [INFO] 状态监听器设置完成 
 [3:05:11 PM] [INFO] UI已切换到工作区 
 [3:05:11 PM] [SUCCESS] 状态管理器初始化完成 
 [3:05:11 PM] [INFO] 开始绑定事件监听器... 
 [3:05:11 PM] [SUCCESS] 所有事件监听器绑定完成 
 [3:05:11 PM] [SUCCESS] 事件管理器初始化完成 
 [3:05:11 PM] [SUCCESS] ✅ 实时分析管理器动画集成成功 
 [3:05:11 PM] [INFO] 实时分析功能已配置（性能优化版） {debounceDelay: 500, minInputLength: 10}
 [3:05:11 PM] [SUCCESS] ✅ 订单输入框事件绑定成功 
 [3:05:11 PM] [INFO] 🌐 使用统一语言检测器，跳过重复绑定 
 [3:05:11 PM] [SUCCESS] ✅ 实时分析事件绑定验证完成 
 [3:05:11 PM] [SUCCESS] ✅ 实时分析管理器初始化完成 
 [3:05:11 PM] [SUCCESS] 管理器模块初始化完成 
 [3:05:11 PM] [SUCCESS] 图片上传按钮事件已重新绑定 
 [3:05:11 PM] [SUCCESS] UI管理器初始化完成 
 [3:05:11 PM] [INFO] 🎨 用户界面初始化完成 
 🎨 用户界面初始化完成
 [3:05:11 PM] [INFO] 📋 执行启动阶段: finalization (5/5) 
 📋 执行启动阶段: finalization (5/5)
 [3:05:11 PM] [INFO] [ApplicationBootstrap] 字段标准化层状态检查 [object Object] 
 [ApplicationBootstrap] 字段标准化层状态检查 {layerExists: true, featureEnabled: true, initialized: true}
 [3:05:11 PM] [INFO] [ApplicationBootstrap] ✅ 字段标准化层已初始化 
 [ApplicationBootstrap] ✅ 字段标准化层已初始化
 [3:05:11 PM] [INFO] 🏁 系统启动完成 
 🏁 系统启动完成
 [3:05:11 PM] [INFO] ✅ OTA系统启动完成，总耗时: 170.70ms 
 ✅ OTA系统启动完成，总耗时: 170.70ms
 📊 启动报告
 [3:05:11 PM] [INFO] ✅ dependencies: 2.50ms 
 ✅ dependencies: 2.50ms
 [3:05:11 PM] [INFO]    详情: 已注册: appState, 已注册: logger, 已注册: utils, 已注册: eventCoordinator, 已注册: apiService, 已注册: geminiService, 已注册: i18nManager, 已注册: formManager, 已注册: imageUploadManager, 已注册: currencyConverter, 已注册: multiOrderManager, 已注册: orderHistoryManager, 已注册: pagingServiceManager, 已注册: uiManager, 已注册: channelDetector 
    详情: 已注册: appState, 已注册: logger, 已注册: utils, 已注册: eventCoordinator, 已注册: apiService, 已注册: geminiService, 已注册: i18nManager, 已注册: formManager, 已注册: imageUploadManager, 已注册: currencyConverter, 已注册: multiOrderManager, 已注册: orderHistoryManager, 已注册: pagingServiceManager, 已注册: uiManager, 已注册: channelDetector
 [3:05:11 PM] [INFO] ✅ services: 32.10ms 
 ✅ services: 32.10ms
 [3:05:11 PM] [INFO]    详情: 已初始化: appState, 已初始化: logger, 已初始化: utils, 已初始化: eventCoordinator, 已初始化: apiService, 已初始化: geminiService 
    详情: 已初始化: appState, 已初始化: logger, 已初始化: utils, 已初始化: eventCoordinator, 已初始化: apiService, 已初始化: geminiService
 [3:05:11 PM] [INFO] ✅ managers: 29.20ms 
 ✅ managers: 29.20ms
 [3:05:11 PM] [INFO]    详情: 已初始化: imageUploadManager, 已初始化: currencyConverter, 已初始化: multiOrderManager, 已初始化: orderHistoryManager, 已初始化: pagingServiceManager 
    详情: 已初始化: imageUploadManager, 已初始化: currencyConverter, 已初始化: multiOrderManager, 已初始化: orderHistoryManager, 已初始化: pagingServiceManager
 [3:05:11 PM] [INFO] ✅ ui: 105.20ms 
 ✅ ui: 105.20ms
 [3:05:11 PM] [INFO]    详情: 国际化管理器已初始化, UI管理器已初始化 
    详情: 国际化管理器已初始化, UI管理器已初始化
 [3:05:11 PM] [INFO] ✅ finalization: 1.10ms 
 ✅ finalization: 1.10ms
 [3:05:11 PM] [INFO]    详情: 健康检查: 90/100, 全局错误处理已设置, 调试接口已暴露, 字段标准化层初始化完成 
    详情: 健康检查: 90/100, 全局错误处理已设置, 调试接口已暴露, 字段标准化层初始化完成
 [3:05:11 PM] [INFO] ✅ OTA系统启动成功，耗时: 170.70ms 
 ✅ OTA系统启动成功，耗时: 170.70ms
 [3:05:11 PM] [INFO] 通过邮箱匹配OTA配置 {email: '<EMAIL>', hasConfig: false}
 [3:05:11 PM] [INFO] 未找到用户专属OTA配置，使用通用配置 {email: '<EMAIL>'}
 [3:05:11 PM] [INFO] 渠道智能检测已启用 {channelCount: 133}
 [3:05:11 PM] [INFO] 自动分析已启用 
 [3:05:11 PM] [INFO] 已启用智能渠道检测和自动分析 {channelCount: 133, autoAnalysisEnabled: true}
 [3:05:11 PM] [INFO] 语言切换后重新填充下拉菜单选项 
 [3:05:11 PM] [INFO] 根据登录邮箱匹配到后台用户 {email: '<EMAIL>', backendUserId: 310}
 [3:05:11 PM] [INFO] 通过邮箱匹配OTA配置 {email: '<EMAIL>', hasConfig: false}
 [3:05:11 PM] [INFO] 未找到用户专属OTA配置，使用通用配置 {email: '<EMAIL>'}
 [3:05:11 PM] [INFO] 渠道智能检测已启用 {channelCount: 133}
 [3:05:11 PM] [INFO] 自动分析已启用 
 [3:05:11 PM] [INFO] 已启用智能渠道检测和自动分析 {channelCount: 133, autoAnalysisEnabled: true}
 [3:05:11 PM] [SUCCESS] 表单选项填充完成 
 [3:05:11 PM] [SUCCESS] 表单选项填充完成 {subCategoriesCount: 3, carTypesCount: 18, backendUsersCount: 43, drivingRegionsCount: 11, elementsStatus: {…}}
 [3:05:11 PM] [INFO] 🔧 [FormManager] 初始化原生语言选择器 
 [3:05:11 PM] [DEBUG] ⚠️ [FormManager] 语言选择器元素不存在，跳过初始化 {availableElements: Array(2)}
 [3:05:11 PM] [INFO] 检测到用户已登录，应用权限控制 {email: '<EMAIL>'}
 [3:05:11 PM] [DEBUG] 用户权限已获取 {identifier: 'jcy@gomyhi...', hasRestrictions: false, cached: true}
 [3:05:11 PM] [INFO] 价格字段权限检查 {email: '<EMAIL>...', permissions: {…}}
 [3:05:11 PM] [INFO] 🔐 开始应用价格字段权限控制 {canViewOtaPrice: true, canViewDriverFee: true}
 [3:05:11 PM] [INFO] 💰 价格信息面板显示状态: block 
 [3:05:11 PM] [INFO] OTA价格字段显示状态: flex 
 [3:05:11 PM] [INFO] 司机费用字段显示状态: flex 
 [3:05:11 PM] [SUCCESS] ✅ 价格字段权限控制应用完成 {canViewOtaPrice: true, canViewDriverFee: true, hasAnyPricePermission: true, priceInfoPanelVisible: true}
 [3:05:11 PM] [INFO] 语言选项权限检查 {email: '<EMAIL>...', permissions: {…}}
 [3:05:11 PM] [INFO] 🔐 开始应用语言选项权限控制 {canUsePaging: true}
 [3:05:11 PM] [SUCCESS] ✅ Paging选项已启用（显示） 
 [3:05:11 PM] [SUCCESS] 语言选项权限控制初始化完成 {canUsePaging: true}
 [3:05:11 PM] [INFO] 渠道权限检查 {email: '<EMAIL>...', restricted: false, allowedChannelsCount: 'unlimited'}
 [3:05:11 PM] [INFO] 🔐 开始应用渠道权限控制 {restricted: false, allowedChannels: null}
 [3:05:11 PM] [INFO] 🌐 用户无渠道限制，保持所有选项 
form-manager.js:40 [Violation] 'setTimeout' handler took 56ms
 [3:05:11 PM] [INFO] 根据登录邮箱匹配到后台用户 {email: '<EMAIL>', backendUserId: 310}
 [3:05:11 PM] [INFO] 已设置默认负责人 {userId: 310}
 [3:05:12 PM] [INFO] 开始缓存预热 
 [3:05:12 PM] [INFO] ✅ 精简酒店数据初始化完成，共 57 家核心酒店 
 ✅ 精简酒店数据初始化完成，共 57 家核心酒店
 [3:05:12 PM] [INFO] 酒店标准化结果已缓存 {hotelName: '香格里拉酒店', source: 'essential_data_prewarm', cacheKey: '[FILTERED]'}
 [3:05:12 PM] [INFO] 酒店标准化结果已缓存 {hotelName: '希尔顿酒店', source: 'essential_data_prewarm', cacheKey: '[FILTERED]'}
 [3:05:12 PM] [INFO] 酒店标准化结果已缓存 {hotelName: '万豪酒店', source: 'essential_data_prewarm', cacheKey: '[FILTERED]'}
 [3:05:12 PM] [INFO] 酒店标准化结果已缓存 {hotelName: '丽思卡尔顿酒店', source: 'essential_data_prewarm', cacheKey: '[FILTERED]'}
 [3:05:12 PM] [INFO] 酒店标准化结果已缓存 {hotelName: '洲际酒店', source: 'essential_data_prewarm', cacheKey: '[FILTERED]'}
 [3:05:12 PM] [INFO] 酒店标准化结果已缓存 {hotelName: '凯悦酒店', source: 'essential_data_prewarm', cacheKey: '[FILTERED]'}
 [3:05:12 PM] [INFO] 酒店标准化结果已缓存 {hotelName: '喜来登酒店', source: 'essential_data_prewarm', cacheKey: '[FILTERED]'}
 [3:05:12 PM] [INFO] 酒店标准化结果已缓存 {hotelName: '威斯汀酒店', source: 'essential_data_prewarm', cacheKey: '[FILTERED]'}
 [3:05:12 PM] [INFO] 酒店标准化结果已缓存 {hotelName: '文华东方酒店', source: 'essential_data_prewarm', cacheKey: '[FILTERED]'}
 [3:05:12 PM] [INFO] 酒店标准化结果已缓存 {hotelName: '四季酒店', source: 'essential_data_prewarm', cacheKey: '[FILTERED]'}
 [3:05:12 PM] [INFO] 酒店标准化结果已缓存 {hotelName: '槟城香格里拉酒店', source: 'essential_data_prewarm', cacheKey: '[FILTERED]'}
 [3:05:12 PM] [INFO] 酒店标准化结果已缓存 {hotelName: '槟城希尔顿酒店', source: 'essential_data_prewarm', cacheKey: '[FILTERED]'}
 [3:05:12 PM] [INFO] 酒店标准化结果已缓存 {hotelName: '东方大酒店', source: 'essential_data_prewarm', cacheKey: '[FILTERED]'}
 [3:05:12 PM] [INFO] 酒店标准化结果已缓存 {hotelName: '槟城万豪酒店', source: 'essential_data_prewarm', cacheKey: '[FILTERED]'}
 [3:05:12 PM] [INFO] 酒店标准化结果已缓存 {hotelName: '新山希尔顿酒店', source: 'essential_data_prewarm', cacheKey: '[FILTERED]'}
 [3:05:12 PM] [INFO] 酒店标准化结果已缓存 {hotelName: '新山万豪酒店', source: 'essential_data_prewarm', cacheKey: '[FILTERED]'}
 [3:05:12 PM] [INFO] 酒店标准化结果已缓存 {hotelName: '亚庇香格里拉酒店', source: 'essential_data_prewarm', cacheKey: '[FILTERED]'}
 [3:05:12 PM] [INFO] 酒店标准化结果已缓存 {hotelName: '亚庇希尔顿酒店', source: 'essential_data_prewarm', cacheKey: '[FILTERED]'}
 [3:05:12 PM] [INFO] 酒店标准化结果已缓存 {hotelName: '兰卡威香格里拉酒店', source: 'essential_data_prewarm', cacheKey: '[FILTERED]'}
 [3:05:12 PM] [INFO] 酒店标准化结果已缓存 {hotelName: '兰卡威丽思卡尔顿酒店', source: 'essential_data_prewarm', cacheKey: '[FILTERED]'}
 [3:05:12 PM] [SUCCESS] 缓存预热完成 {prewarmedCount: 20}
google-maps-service.js:61 [Violation] 'setTimeout' handler took 54ms
 [3:05:13 PM] [INFO] 🔍 开始系统完整性检查 
 [3:05:13 PM] [INFO] 🔗 检查依赖关系... 
 [3:05:13 PM] [INFO] 📋 检查服务注册... 
 [3:05:13 PM] [INFO] 🎭 检查事件系统... 
 [3:05:13 PM] [INFO] 🚀 检查初始化逻辑... 
 [3:05:13 PM] [INFO] 🔄 检查向后兼容性... 
 [3:05:13 PM] [INFO] 🏁 系统完整性检查完成 {overall: 'ISSUES_FOUND', summary: {…}, categories: {…}, failedTests: Array(4), results: Array(25)}
 [3:05:13 PM] [INFO] [GlobalFieldStandardization] ℹ️ 自动初始化检查已停止 
 [GlobalFieldStandardization] ℹ️ 自动初始化检查已停止
 [3:05:21 PM] [INFO] 🌐 检测到英文内容，已自动设置英文语言要求 {sourceField: '#pickup', textLength: 2, hasChinese: false, languageIds: Array(1)}
 [3:05:21 PM] [INFO] 渠道检测结果已存储到AppState {channel: null, confidence: 0, method: 'no_match', sourceField: '#pickup', timestamp: 1755068721401, …}
 [3:05:21 PM] [INFO] 🌐 检测到英文内容，已自动设置英文语言要求 {sourceField: '#pickup', textLength: 4, hasChinese: false, languageIds: Array(1)}
 [3:05:21 PM] [INFO] 渠道检测结果已存储到AppState {channel: null, confidence: 0, method: 'no_match', sourceField: '#pickup', timestamp: 1755068721712, …}
 [3:05:24 PM] [INFO] 🌐 检测到英文内容，已自动设置英文语言要求 {sourceField: '#pickup', textLength: 5, hasChinese: false, languageIds: Array(1)}
 [3:05:24 PM] [INFO] 渠道检测结果已存储到AppState {channel: null, confidence: 0, method: 'no_match', sourceField: '#pickup', timestamp: 1755068724833, …}
 [3:05:25 PM] [INFO] 🌐 检测到英文内容，已自动设置英文语言要求 {sourceField: '#pickup', textLength: 4, hasChinese: false, languageIds: Array(1)}
 [3:05:25 PM] [INFO] 渠道检测结果已存储到AppState {channel: null, confidence: 0, method: 'no_match', sourceField: '#pickup', timestamp: 1755068725615, …}
 [3:05:26 PM] [INFO] 🌐 检测到英文内容，已自动设置英文语言要求 {sourceField: '#pickup', textLength: 3, hasChinese: false, languageIds: Array(1)}
 [3:05:26 PM] [INFO] 渠道检测结果已存储到AppState {channel: null, confidence: 0, method: 'no_match', sourceField: '#pickup', timestamp: 1755068726048, …}
 [3:05:26 PM] [INFO] 🌐 检测到英文内容，已自动设置英文语言要求 {sourceField: '#pickup', textLength: 2, hasChinese: false, languageIds: Array(1)}
 [3:05:26 PM] [INFO] 渠道检测结果已存储到AppState {channel: null, confidence: 0, method: 'no_match', sourceField: '#pickup', timestamp: 1755068726227, …}
 [3:05:26 PM] [INFO] 🌐 检测到英文内容，已自动设置英文语言要求 {sourceField: '#pickup', textLength: 1, hasChinese: false, languageIds: Array(1)}
 [3:05:26 PM] [INFO] 渠道检测结果已存储到AppState {channel: null, confidence: 0, method: 'no_match', sourceField: '#pickup', timestamp: 1755068726448, …}
 [3:05:30 PM] [INFO] 实时分析已禁用 
 [3:05:39 PM] [INFO] 实时分析已启用 
 [3:05:40 PM] [INFO] [GlobalFieldStandardization] 🔄 执行延迟重试拦截器安装... 
 [GlobalFieldStandardization] 🔄 执行延迟重试拦截器安装...
 [3:05:40 PM] [INFO] [GlobalFieldStandardization] 尝试安装Gemini拦截器 (1/150) [object Object] 
 [GlobalFieldStandardization] 尝试安装Gemini拦截器 (1/150) {hasAdapter: true, hasCaller: true, adapterHasParseOrder: true, adapterHasLegacyParse: false, callerHasParseAPIResponse: true}
 [3:05:40 PM] [INFO] [GlobalFieldStandardization] ✅ Gemini适配器拦截器已安装 (parseOrder) 
 [GlobalFieldStandardization] ✅ Gemini适配器拦截器已安装 (parseOrder)
 [3:05:40 PM] [INFO] [DependencyContainer] 已创建服务实例: formManager 
 [3:05:40 PM] [INFO] [GlobalFieldStandardization] ⚠️ 表单管理器服务未在依赖容器中注册，跳过拦截 
 [GlobalFieldStandardization] ⚠️ 表单管理器服务未在依赖容器中注册，跳过拦截
 [3:05:40 PM] [INFO] [GlobalFieldStandardization] ✅ 延迟重试完成（已简化） 
 [GlobalFieldStandardization] ✅ 延迟重试完成（已简化）
 [3:05:40 PM] [INFO] 🌐 检测到中文内容，已自动设置中文语言要求 {sourceField: 'unknown', textLength: 252, hasChinese: true, languageIds: Array(1)}
 [3:05:40 PM] [DEBUG] OTA渠道下拉框未找到 {searchedSelectors: Array(5)}
 [3:05:40 PM] [INFO] 渠道检测结果已存储到AppState {channel: 'Fliggy', confidence: 0.95, method: 'fliggy_pattern', matchedPattern: '订单编号[：:\\s]*\\d{19}', sourceField: 'unknown', …}
 [3:05:40 PM] [INFO] 🚀 渠道检测完成并已存储结果 {channel: 'Fliggy', confidence: 0.95, sourceField: 'unknown', stored: true}
 [3:05:40 PM] [INFO] ⚠️ 检测到重复输入调用，跳过处理 {timeDiff: 93, isPasteEvent: true}
 🔍 多订单数据流追踪 - 第1步：实时分析触发
 [3:05:41 PM] [INFO] 输入文本长度: 252 
 输入文本长度: 252
 [3:05:41 PM] [INFO] 输入文本预览: 订单编号：2565400587815208968买家：懒懒qirls支付时间：2025-05-15 13:19:08
查看详情

舒适5座

【送机】

马来西亚-吉隆坡

[出发]吉隆坡唐人街福朋喜来登酒店

[抵达]吉隆坡国际机场T1

约53.8公里

2025-05-16 11:10:00

邱敏华

真实号：13788934279

---
1成人0儿童

司机姓名：bong

司机电话... 
 输入文本预览: 订单编号：2565400587815208968买家：懒懒qirls支付时间：2025-05-15 13:19:08
查看详情

舒适5座

【送机】

马来西亚-吉隆坡

[出发]吉隆坡唐人街福朋喜来登酒店

[抵达]吉隆坡国际机场T1

约53.8公里

2025-05-16 11:10:00

邱敏华

真实号：13788934279

---
1成人0儿童

司机姓名：bong

司机电话...
 [3:05:41 PM] [INFO] 🔍 获取到的渠道检测结果: [object Object] 
 🔍 获取到的渠道检测结果: {channel: 'Fliggy', confidence: 0.95, method: 'fliggy_pattern', matchedPattern: '订单编号[：:\\s]*\\d{19}', sourceField: 'unknown', …}
realtime-analysis-manager.js:449 🔍 多订单数据流追踪 - 第2步：调用Gemini解析
logger.js:473 [3:05:41 PM] [INFO] 🔄 开始实时订单解析... 
logger.js:473 [3:05:41 PM] [INFO] [GlobalFieldStandardization] 🔧 拦截到Gemini调用 parseOrder 
logger.js:199 [GlobalFieldStandardization] 🔧 拦截到Gemini调用 parseOrder
logger.js:473 [3:05:41 PM] [INFO] 适配器：解析订单文本 {textLength: 252, isRealtime: true, hasChannelInfo: true}
logger.js:473 [3:05:41 PM] [INFO] 开始处理输入 {type: 'text', inputLength: 252, autoTriggered: false, sourceField: 'unknown'}
logger.js:473 [3:05:41 PM] [INFO] 🚀 使用预检测的渠道结果 {channel: 'Fliggy', confidence: 0.95, method: 'fliggy_pattern'}
logger.js:473 [3:05:41 PM] [DEBUG] 🔧 [调试] 开始构建提示词 {channel: 'Fliggy', confidence: 0.95, hasStrategy: true}
logger.js:473 [3:05:41 PM] [INFO] 开始构建提示词 {channel: 'Fliggy', inputLength: 252, autoTriggered: false, sourceField: 'unknown'}
logger.js:473 [3:05:41 PM] [DEBUG] 🔧 [调试] 获取渠道策略 {channel: 'Fliggy'}
logger.js:473 [3:05:41 PM] [DEBUG] 🔧 [调试] 渠道策略找到，获取字段片段 {strategyType: 'FliggyOTAStrategy', hasGetFieldPromptSnippets: true}
logger.js:470 [3:05:41 PM] [SUCCESS] 成功获取字段提示词片段 {snippetCount: 7}
logger.js:473 [3:05:41 PM] [DEBUG] 🔧 [调试] 字段片段获取完成 {snippetCount: 7, snippetKeys: '[FILTERED]'}
logger.js:470 [3:05:41 PM] [SUCCESS] 提示词构建完成 {channel: 'Fliggy', promptLength: 5416, autoTriggered: false}
logger.js:473 [3:05:41 PM] [DEBUG] 🔧 [调试] 提示词构建完成 {promptLength: 5416, containsChannelInfo: true}
logger.js:473 [3:05:41 PM] [INFO] 开始调用Gemini API {type: 'text', promptLength: 5416, isRealtime: true}
logger.js:470 [3:05:48 PM] [SUCCESS] Gemini API调用成功: 7549.20ms {timeout: 25000, model: 'gemini-2.5-flash'}
logger.js:473 [3:05:48 PM] [INFO] 🔍 Gemini API完整响应: {
  "candidates": [
    {
      "content": {
        "parts": [
          {
            "text": "{\n  \"customer_name\": \"邱敏华\",\n  \"customer_contact\": \"13788934279\",\n  \"customer_email\": null,\n  \"ota\": \"Fliggy\",\n  \"ota_reference_number\": \"2565400587815208968\",\n  \"flight_info\": null,\n  \"departure_time\": null,\n  \"arrival_time\": null,\n  \"flight_type\": \"Departure\",\n  \"date\": \"2025-05-16\",\n  \"time\": \"11:10\",\n  \"pickup\": \"吉隆坡唐人街福朋喜来登酒店\",\n  \"destination\": \"吉隆坡国际机场T1\",\n  \"passenger_number\": 1,\n  \"luggage_number\": null,\n  \"sub_category_id\": 3,\n  \"car_type_id\": 5,\n  \"driving_region_id\": 1,\n  \"baby_chair\": null,\n  \"tour_guide\": null,\n  \"meet_and_greet\": null,\n  \"needs_paging_service\": null,\n  \"ota_price\": 72.25,\n  \"currency\": \"MYR\",\n  \"extra_requirement\": null\n}"
          }
        ],
        "role": "model"
      },
      "finishReason": "STOP",
      "index": 0
    }
  ],
  "usageMetadata": {
    "promptTokenCount": 2675,
    "candidatesTokenCount": 310,
    "totalTokenCount": 3946,
    "promptTokensDetails": [
      {
        "modality": "TEXT",
        "tokenCount": 2675
      }
    ],
    "thoughtsTokenCount": 961
  },
  "modelVersion": "gemini-2.5-flash",
  "responseId": "TTmcaPeFB7Gcz7IPhLe4yQo"
} 
logger.js:199 🔍 Gemini API完整响应: {
  "candidates": [
    {
      "content": {
        "parts": [
          {
            "text": "{\n  \"customer_name\": \"邱敏华\",\n  \"customer_contact\": \"13788934279\",\n  \"customer_email\": null,\n  \"ota\": \"Fliggy\",\n  \"ota_reference_number\": \"2565400587815208968\",\n  \"flight_info\": null,\n  \"departure_time\": null,\n  \"arrival_time\": null,\n  \"flight_type\": \"Departure\",\n  \"date\": \"2025-05-16\",\n  \"time\": \"11:10\",\n  \"pickup\": \"吉隆坡唐人街福朋喜来登酒店\",\n  \"destination\": \"吉隆坡国际机场T1\",\n  \"passenger_number\": 1,\n  \"luggage_number\": null,\n  \"sub_category_id\": 3,\n  \"car_type_id\": 5,\n  \"driving_region_id\": 1,\n  \"baby_chair\": null,\n  \"tour_guide\": null,\n  \"meet_and_greet\": null,\n  \"needs_paging_service\": null,\n  \"ota_price\": 72.25,\n  \"currency\": \"MYR\",\n  \"extra_requirement\": null\n}"
          }
        ],
        "role": "model"
      },
      "finishReason": "STOP",
      "index": 0
    }
  ],
  "usageMetadata": {
    "promptTokenCount": 2675,
    "candidatesTokenCount": 310,
    "totalTokenCount": 3946,
    "promptTokensDetails": [
      {
        "modality": "TEXT",
        "tokenCount": 2675
      }
    ],
    "thoughtsTokenCount": 961
  },
  "modelVersion": "gemini-2.5-flash",
  "responseId": "TTmcaPeFB7Gcz7IPhLe4yQo"
}
logger.js:473 [3:05:48 PM] [INFO] 🔍 第一个候选结果: {
  "content": {
    "parts": [
      {
        "text": "{\n  \"customer_name\": \"邱敏华\",\n  \"customer_contact\": \"13788934279\",\n  \"customer_email\": null,\n  \"ota\": \"Fliggy\",\n  \"ota_reference_number\": \"2565400587815208968\",\n  \"flight_info\": null,\n  \"departure_time\": null,\n  \"arrival_time\": null,\n  \"flight_type\": \"Departure\",\n  \"date\": \"2025-05-16\",\n  \"time\": \"11:10\",\n  \"pickup\": \"吉隆坡唐人街福朋喜来登酒店\",\n  \"destination\": \"吉隆坡国际机场T1\",\n  \"passenger_number\": 1,\n  \"luggage_number\": null,\n  \"sub_category_id\": 3,\n  \"car_type_id\": 5,\n  \"driving_region_id\": 1,\n  \"baby_chair\": null,\n  \"tour_guide\": null,\n  \"meet_and_greet\": null,\n  \"needs_paging_service\": null,\n  \"ota_price\": 72.25,\n  \"currency\": \"MYR\",\n  \"extra_requirement\": null\n}"
      }
    ],
    "role": "model"
  },
  "finishReason": "STOP",
  "index": 0
} 
logger.js:199 🔍 第一个候选结果: {
  "content": {
    "parts": [
      {
        "text": "{\n  \"customer_name\": \"邱敏华\",\n  \"customer_contact\": \"13788934279\",\n  \"customer_email\": null,\n  \"ota\": \"Fliggy\",\n  \"ota_reference_number\": \"2565400587815208968\",\n  \"flight_info\": null,\n  \"departure_time\": null,\n  \"arrival_time\": null,\n  \"flight_type\": \"Departure\",\n  \"date\": \"2025-05-16\",\n  \"time\": \"11:10\",\n  \"pickup\": \"吉隆坡唐人街福朋喜来登酒店\",\n  \"destination\": \"吉隆坡国际机场T1\",\n  \"passenger_number\": 1,\n  \"luggage_number\": null,\n  \"sub_category_id\": 3,\n  \"car_type_id\": 5,\n  \"driving_region_id\": 1,\n  \"baby_chair\": null,\n  \"tour_guide\": null,\n  \"meet_and_greet\": null,\n  \"needs_paging_service\": null,\n  \"ota_price\": 72.25,\n  \"currency\": \"MYR\",\n  \"extra_requirement\": null\n}"
      }
    ],
    "role": "model"
  },
  "finishReason": "STOP",
  "index": 0
}
logger.js:470 [3:05:48 PM] [SUCCESS] Gemini API调用成功 {type: 'text', hasResult: true}
logger.js:473 [3:05:48 PM] [INFO] 开始处理结果 {hasGeminiResult: true, channel: 'Fliggy'}
logger.js:470 [3:05:48 PM] [SUCCESS] 结果处理完成 {type: 'single-order', orderCount: 1}
logger.js:470 [3:05:48 PM] [SUCCESS] 输入处理完成 {channel: 'Fliggy', resultType: 'single-order', autoTriggered: false}
logger.js:473 [3:05:48 PM] [INFO] [GeminiServiceAdapter] 🔧 业务流控制器原始结果: [object Object] 
logger.js:199 [GeminiServiceAdapter] 🔧 业务流控制器原始结果: {type: 'single-order', order: {…}, orders: Array(1), channel: 'Fliggy', confidence: 0.8, …}
gemini-service-adapter.js:218 🤖 Gemini parseOrder 返回
logger.js:473 [3:05:48 PM] [INFO] meta: [object Object] 
logger.js:199 meta: {isRealtime: true}
logger.js:473 [3:05:48 PM] [INFO] data (object): [object Object] 
logger.js:199 data (object): [{…}]
logger.js:473 [3:05:48 PM] [INFO] data (json):
[
  {
    "customer_name": "邱敏华",
    "customer_contact": "13788934279",
    "customer_email": null,
    "ota": "Fliggy",
    "ota_reference_number": "2565400587815208968",
    "flight_info": null,
    "departure_time": null,
    "arrival_time": null,
    "flight_type": "Departure",
    "date": "2025-05-16",
    "time": "11:10",
    "pickup": "吉隆坡唐人街福朋喜来登酒店",
    "destination": "吉隆坡国际机场T1",
    "passenger_number": 1,
    "luggage_number": null,
    "sub_category_id": 3,
    "car_type_id": 5,
    "driving_region_id": 1,
    "baby_chair": null,
    "tour_guide": null,
    "meet_and_greet": null,
    "needs_paging_service": null,
    "ota_price": 72.25,
    "currency": "MYR",
    "extra_requirement": null,
    "orderIndex": 0,
    "processedAt": "2025-08-13T07:05:48.987Z"
  }
] 
logger.js:199 data (json):
[
  {
    "customer_name": "邱敏华",
    "customer_contact": "13788934279",
    "customer_email": null,
    "ota": "Fliggy",
    "ota_reference_number": "2565400587815208968",
    "flight_info": null,
    "departure_time": null,
    "arrival_time": null,
    "flight_type": "Departure",
    "date": "2025-05-16",
    "time": "11:10",
    "pickup": "吉隆坡唐人街福朋喜来登酒店",
    "destination": "吉隆坡国际机场T1",
    "passenger_number": 1,
    "luggage_number": null,
    "sub_category_id": 3,
    "car_type_id": 5,
    "driving_region_id": 1,
    "baby_chair": null,
    "tour_guide": null,
    "meet_and_greet": null,
    "needs_paging_service": null,
    "ota_price": 72.25,
    "currency": "MYR",
    "extra_requirement": null,
    "orderIndex": 0,
    "processedAt": "2025-08-13T07:05:48.987Z"
  }
]
logger.js:473 [3:05:48 PM] [INFO] Gemini parseResult: [object Object] 
logger.js:199 Gemini parseResult: [{…}]
logger.js:473 [3:05:48 PM] [INFO] 🔍 parseOrder返回结果调试 {parseResult: Array(1), isArray: true, length: 1, type: 'object'}
logger.js:470 [3:05:48 PM] [SUCCESS] ✅ 解析完成，检测到 1 个订单 
realtime-analysis-manager.js:519 🔍 单订单数据流追踪 - 第4步：简化渠道检测
logger.js:473 [3:05:48 PM] [INFO] 🔍 开始单订单渠道检测 {orderData: {…}, originalText: '订单编号：2565400587815208968买家：懒懒qirls支付时间：2025-05-15 …605088835\n\n---\n\n总价格：140元\n\n用户实付：140.00元\n\n商家实收：140元'}
logger.js:473 [3:05:49 PM] [INFO] 开始渠道检测 {inputLength: 252}
logger.js:470 [3:05:49 PM] [SUCCESS] 检测到Fliggy渠道 {channel: 'fliggy', confidence: 0.95, method: 'fliggy_pattern', matchedPattern: '订单编号+19位数字'}
logger.js:470 [3:05:49 PM] [SUCCESS] ✅ 统一渠道检测完成 {}
logger.js:473 [3:05:49 PM] [INFO] 开始从当前订单状态更新表单 
logger.js:473 [3:05:49 PM] [INFO] 🚀 启用实时填充模式（禁用动画） 
logger.js:473 [3:05:49 PM] [INFO] 开始填充表单数据 {dataKeys: '[FILTERED]'}
logger.js:473 [3:05:49 PM] [INFO] 字段映射: customer_name → customerName {value: '邱敏华', hasElement: true}
logger.js:473 [3:05:49 PM] [INFO] 字段映射: customer_contact → customerContact {value: '13788934279', hasElement: true}
logger.js:473 [3:05:49 PM] [INFO] 字段映射: customer_email → customerEmail {value: null, hasElement: true}
logger.js:473 [3:05:49 PM] [INFO] 特殊处理OTA渠道字段: ota {value: 'Fliggy'}
logger.js:473 [3:05:49 PM] [INFO] OTA渠道设置成功(下拉框): Fliggy 
logger.js:473 [3:05:49 PM] [INFO] 字段映射: ota_reference_number → otaReferenceNumber {value: '2565400587815208968', hasElement: true}
logger.js:473 [3:05:49 PM] [INFO] 字段映射: departure_time → pickupTime {value: null, hasElement: true}
logger.js:473 [3:05:49 PM] [INFO] 字段映射: arrival_time → pickupTime {value: null, hasElement: true}
logger.js:473 [3:05:49 PM] [INFO] 字段映射: date → pickupDate {value: '2025-05-16', hasElement: true}
logger.js:473 [3:05:49 PM] [INFO] 字段映射: time → pickupTime {value: '11:10', hasElement: true}
logger.js:473 [3:05:49 PM] [INFO] 字段映射: pickup → pickup {value: '吉隆坡唐人街福朋喜来登酒店', hasElement: true}
logger.js:473 [3:05:49 PM] [INFO] 字段映射: destination → dropoff {value: '吉隆坡国际机场T1', hasElement: true}
logger.js:473 [3:05:49 PM] [INFO] 字段映射: passenger_number → passengerCount {value: 1, hasElement: true}
logger.js:473 [3:05:49 PM] [INFO] 字段映射: luggage_number → luggageCount {value: null, hasElement: true}
logger.js:473 [3:05:49 PM] [INFO] 字段映射: sub_category_id → subCategoryId {value: 3, hasElement: true}
logger.js:473 [3:05:49 PM] [INFO] 字段映射: car_type_id → carTypeId {value: 5, hasElement: true}
logger.js:473 [3:05:49 PM] [DEBUG] [VehicleConfigManager] 车型推荐完成 {passengerCount: 1, otaChannel: null, recommendedCarTypeId: 5, recommendedName: '5 Seater'}
logger.js:473 [3:05:49 PM] [INFO] 使用车型配置管理器推荐车型 {passengerCount: 1, recommendedCarTypeId: 5, recommendedName: '5 Seater', description: '推荐给1位乘客的5 Seater'}
logger.js:473 [3:05:49 PM] [INFO] 字段映射: driving_region_id → drivingRegionId {value: 1, hasElement: true}
logger.js:473 [3:05:49 PM] [INFO] 字段映射: ota_price → otaPrice {value: 72.25, hasElement: true}
logger.js:473 [3:05:49 PM] [INFO] 字段映射: currency → currency {value: 'MYR', hasElement: true}
logger.js:473 [3:05:49 PM] [INFO] 字段映射: extra_requirement → extraRequirement {value: null, hasElement: true}
logger.js:473 [3:05:49 PM] [INFO] 根据登录邮箱匹配到后台用户 {email: '<EMAIL>', backendUserId: 310}
logger.js:473 [3:05:49 PM] [INFO] 字段设置成功: inchargeByBackendUserId = 310 
logger.js:473 [3:05:49 PM] [INFO] 已应用默认负责人: 310 
logger.js:473 [3:05:49 PM] [INFO] 🚀 实时填充模式已完成 
logger.js:470 [3:05:49 PM] [SUCCESS] 表单数据填充完成 {fieldsProcessed: 27, realtimeMode: false}
logger.js:473 [3:05:49 PM] [INFO] 🚀 触发地址处理流水线 {hasPickup: true, hasDropoff: true}
logger.js:473 [3:05:49 PM] [INFO] 🚀 启用实时填充模式（禁用动画） 
logger.js:473 [3:05:49 PM] [INFO] 开始填充表单数据 {dataKeys: '[FILTERED]'}
logger.js:473 [3:05:49 PM] [INFO] 字段映射: customer_name → customerName {value: '邱敏华', hasElement: true}
logger.js:473 [3:05:49 PM] [INFO] 字段映射: customer_contact → customerContact {value: '13788934279', hasElement: true}
logger.js:473 [3:05:49 PM] [INFO] 字段映射: customer_email → customerEmail {value: null, hasElement: true}
logger.js:473 [3:05:49 PM] [INFO] 特殊处理OTA渠道字段: ota {value: 'Fliggy'}
logger.js:473 [3:05:49 PM] [INFO] OTA渠道设置成功(下拉框): Fliggy 
logger.js:473 [3:05:49 PM] [INFO] 字段映射: ota_reference_number → otaReferenceNumber {value: '2565400587815208968', hasElement: true}
logger.js:473 [3:05:49 PM] [INFO] 字段映射: departure_time → pickupTime {value: null, hasElement: true}
logger.js:473 [3:05:49 PM] [INFO] 字段映射: arrival_time → pickupTime {value: null, hasElement: true}
logger.js:473 [3:05:49 PM] [INFO] 字段映射: date → pickupDate {value: '2025-05-16', hasElement: true}
logger.js:473 [3:05:49 PM] [INFO] 字段映射: time → pickupTime {value: '11:10', hasElement: true}
logger.js:473 [3:05:49 PM] [INFO] 字段映射: pickup → pickup {value: '吉隆坡唐人街福朋喜来登酒店', hasElement: true}
logger.js:473 [3:05:49 PM] [INFO] 字段映射: destination → dropoff {value: '吉隆坡国际机场T1', hasElement: true}
logger.js:473 [3:05:49 PM] [INFO] 字段映射: passenger_number → passengerCount {value: 1, hasElement: true}
logger.js:473 [3:05:49 PM] [INFO] 字段映射: luggage_number → luggageCount {value: null, hasElement: true}
logger.js:473 [3:05:49 PM] [INFO] 字段映射: sub_category_id → subCategoryId {value: 3, hasElement: true}
logger.js:473 [3:05:49 PM] [INFO] 字段映射: car_type_id → carTypeId {value: 5, hasElement: true}
logger.js:473 [3:05:49 PM] [DEBUG] [VehicleConfigManager] 车型推荐完成 {passengerCount: 1, otaChannel: null, recommendedCarTypeId: 5, recommendedName: '5 Seater'}
logger.js:473 [3:05:49 PM] [INFO] 使用车型配置管理器推荐车型 {passengerCount: 1, recommendedCarTypeId: 5, recommendedName: '5 Seater', description: '推荐给1位乘客的5 Seater'}
logger.js:473 [3:05:49 PM] [INFO] 字段映射: driving_region_id → drivingRegionId {value: 1, hasElement: true}
logger.js:473 [3:05:49 PM] [INFO] 字段映射: ota_price → otaPrice {value: 72.25, hasElement: true}
logger.js:473 [3:05:49 PM] [INFO] 字段映射: currency → currency {value: 'MYR', hasElement: true}
logger.js:473 [3:05:49 PM] [INFO] 字段映射: extra_requirement → extraRequirement {value: null, hasElement: true}
logger.js:473 [3:05:49 PM] [INFO] 根据登录邮箱匹配到后台用户 {email: '<EMAIL>', backendUserId: 310}
logger.js:473 [3:05:49 PM] [INFO] 字段设置成功: inchargeByBackendUserId = 310 
logger.js:473 [3:05:49 PM] [INFO] 已应用默认负责人: 310 
logger.js:473 [3:05:49 PM] [INFO] 🚀 实时填充模式已完成 
logger.js:470 [3:05:49 PM] [SUCCESS] 表单数据填充完成 {fieldsProcessed: 27, realtimeMode: false}
logger.js:470 [3:05:49 PM] [SUCCESS] 🚀 实时表单填充完成 
logger.js:470 [3:05:49 PM] [SUCCESS] 实时分析完成 {confidence: 35, dataKeys: '[FILTERED]'}
logger.js:470 [3:05:49 PM] [SUCCESS] ✅ 单订单处理完成（简化渠道检测） 
logger.js:473 [3:05:49 PM] [INFO] 🔄 实时分析处理完成 
logger.js:473 [3:05:49 PM] [INFO] 🚀 开始批量执行19个DOM更新操作 
logger.js:473 [3:05:49 PM] [INFO] 字段设置成功: customerName = 邱敏华 
logger.js:473 [3:05:49 PM] [INFO] 字段设置成功: customerContact = 13788934279 
logger.js:473 [3:05:49 PM] [INFO] 字段设置成功: customerEmail = null 
logger.js:473 [3:05:49 PM] [INFO] 字段设置成功: otaReferenceNumber = 2565400587815208968 
logger.js:473 [3:05:49 PM] [INFO] 字段设置成功: flightInfo = null 
logger.js:473 [3:05:49 PM] [INFO] 字段设置成功: pickupTime = null 
logger.js:473 [3:05:49 PM] [INFO] 字段设置成功: pickupTime = null 
logger.js:473 [3:05:49 PM] [INFO] 字段设置成功: pickupDate = 2025-05-16 
logger.js:473 [3:05:49 PM] [INFO] 日期字段填充: pickupDate {original: '2025-05-16', formatted: '2025-05-16'}
logger.js:473 [3:05:49 PM] [INFO] 字段设置成功: pickupTime = 11:10 
logger.js:473 [3:05:49 PM] [INFO] 时间字段填充: pickupTime {original: '11:10', formatted: '11:10', primary: false}
logger.js:473 [3:05:49 PM] [INFO] 字段设置成功: pickup = 吉隆坡唐人街福朋喜来登酒店 
logger.js:473 [3:05:49 PM] [INFO] 字段设置成功: dropoff = 吉隆坡国际机场T1 
logger.js:473 [3:05:49 PM] [INFO] 字段设置成功: passengerCount = 1 
logger.js:473 [3:05:49 PM] [INFO] 字段设置成功: luggageCount = null 
logger.js:473 [3:05:49 PM] [INFO] 下拉框设置成功: subCategoryId = 3 
logger.js:473 [3:05:49 PM] [INFO] 下拉框设置成功: carTypeId = 5 
logger.js:473 [3:05:49 PM] [INFO] 下拉框设置成功: drivingRegionId = 1 
logger.js:473 [3:05:49 PM] [INFO] 字段设置成功: otaPrice = 72.25 
logger.js:473 [3:05:49 PM] [INFO] 下拉框设置成功: currency = MYR 
logger.js:473 [3:05:49 PM] [INFO] 字段设置成功: extraRequirement = null 
logger.js:470 [3:05:49 PM] [SUCCESS] 🚀 批量DOM更新完成 
form-manager.js:1091 [Violation] 'requestAnimationFrame' handler took 54ms
logger.js:473 [3:05:49 PM] [INFO] 🚀 开始批量执行19个DOM更新操作 
logger.js:473 [3:05:49 PM] [INFO] 字段设置成功: customerName = 邱敏华 
logger.js:473 [3:05:49 PM] [INFO] 字段设置成功: customerContact = 13788934279 
logger.js:473 [3:05:49 PM] [INFO] 字段设置成功: customerEmail = null 
logger.js:473 [3:05:49 PM] [INFO] 字段设置成功: otaReferenceNumber = 2565400587815208968 
logger.js:473 [3:05:49 PM] [INFO] 字段设置成功: flightInfo = null 
logger.js:473 [3:05:49 PM] [INFO] 字段设置成功: pickupTime = null 
logger.js:473 [3:05:49 PM] [INFO] 字段设置成功: pickupTime = null 
logger.js:473 [3:05:49 PM] [INFO] 字段设置成功: pickupDate = 2025-05-16 
logger.js:473 [3:05:49 PM] [INFO] 日期字段填充: pickupDate {original: '2025-05-16', formatted: '2025-05-16'}
logger.js:473 [3:05:49 PM] [INFO] 字段设置成功: pickupTime = 11:10 
logger.js:473 [3:05:49 PM] [INFO] 时间字段填充: pickupTime {original: '11:10', formatted: '11:10', primary: false}
logger.js:473 [3:05:49 PM] [INFO] 字段设置成功: pickup = 吉隆坡唐人街福朋喜来登酒店 
logger.js:473 [3:05:49 PM] [INFO] 字段设置成功: dropoff = 吉隆坡国际机场T1 
logger.js:473 [3:05:49 PM] [INFO] 字段设置成功: passengerCount = 1 
logger.js:473 [3:05:49 PM] [INFO] 字段设置成功: luggageCount = null 
logger.js:473 [3:05:49 PM] [INFO] 下拉框设置成功: subCategoryId = 3 
logger.js:473 [3:05:49 PM] [INFO] 下拉框设置成功: carTypeId = 5 
logger.js:473 [3:05:49 PM] [INFO] 下拉框设置成功: drivingRegionId = 1 
logger.js:473 [3:05:49 PM] [INFO] 字段设置成功: otaPrice = 72.25 
logger.js:473 [3:05:49 PM] [INFO] 下拉框设置成功: currency = MYR 
logger.js:473 [3:05:49 PM] [INFO] 字段设置成功: extraRequirement = null 
logger.js:470 [3:05:49 PM] [SUCCESS] 🚀 批量DOM更新完成 
logger.js:473 [3:05:49 PM] [INFO] 开始异步处理地址列表 {addressCount: 2}
logger.js:473 [3:05:49 PM] [INFO] 初始化地址处理流水线协调器 
logger.js:473 [3:05:49 PM] [INFO] 初始化地址处理流水线协调器 
logger.js:470 [3:05:49 PM] [SUCCESS] Google Maps 服务已连接 
logger.js:470 [3:05:49 PM] [SUCCESS] 地址翻译器已连接 
logger.js:470 [3:05:49 PM] [SUCCESS] 知识库已连接 
logger.js:470 [3:05:49 PM] [SUCCESS] Google Maps 服务已连接 
logger.js:470 [3:05:49 PM] [SUCCESS] 地址翻译器已连接 
logger.js:470 [3:05:49 PM] [SUCCESS] 知识库已连接 
logger.js:470 [3:05:49 PM] [SUCCESS] ✅ 地址处理流水线协调器初始化完成 
logger.js:470 [3:05:49 PM] [SUCCESS] ✅ 地址处理流水线协调器初始化完成 
logger.js:473 [3:05:49 PM] [INFO] 开始处理地址 {address: '吉隆坡唐人街福朋喜来登酒店', options: {…}}
logger.js:473 [3:05:49 PM] [INFO] 执行流水线步骤: local_mapping 
logger.js:473 [3:05:49 PM] [INFO] 🔍 完整数据模糊匹配: 吉隆坡唐人街福朋喜来登酒店 → Shangri-La Hotel 
logger.js:199 🔍 完整数据模糊匹配: 吉隆坡唐人街福朋喜来登酒店 → Shangri-La Hotel
logger.js:473 [3:05:49 PM] [INFO] 开始翻译地址 {address: '吉隆坡唐人街福朋喜来登酒店', targetLanguages: Array(2)}
logger.js:473 [3:05:49 PM] [INFO] 🔍 完整数据模糊匹配: 吉隆坡唐人街福朋喜来登酒店 → Shangri-La Hotel 
logger.js:199 🔍 完整数据模糊匹配: 吉隆坡唐人街福朋喜来登酒店 → Shangri-La Hotel
logger.js:473 [3:05:49 PM] [INFO] 开始处理地址 {address: '吉隆坡国际机场T1', options: {…}}
logger.js:473 [3:05:49 PM] [INFO] 执行流水线步骤: local_mapping 
logger.js:473 [3:05:49 PM] [INFO] 🔍 完整数据模糊匹配: 吉隆坡国际机场T1 → Shangri-La Hotel 
logger.js:199 🔍 完整数据模糊匹配: 吉隆坡国际机场T1 → Shangri-La Hotel
logger.js:473 [3:05:49 PM] [INFO] 开始翻译地址 {address: '吉隆坡国际机场T1', targetLanguages: Array(2)}
logger.js:473 [3:05:49 PM] [INFO] 🔍 完整数据模糊匹配: 吉隆坡国际机场T1 → Shangri-La Hotel 
logger.js:199 🔍 完整数据模糊匹配: 吉隆坡国际机场T1 → Shangri-La Hotel
logger.js:473 [3:05:49 PM] [INFO] 🔍 完整数据模糊匹配: 吉隆坡唐人街福朋喜来登酒店 → Shangri-La Hotel 
logger.js:199 🔍 完整数据模糊匹配: 吉隆坡唐人街福朋喜来登酒店 → Shangri-La Hotel
logger.js:473 [3:05:49 PM] [INFO] 🔍 完整数据模糊匹配: 吉隆坡国际机场T1 → Shangri-La Hotel 
logger.js:199 🔍 完整数据模糊匹配: 吉隆坡国际机场T1 → Shangri-La Hotel
logger.js:470 [3:05:49 PM] [SUCCESS] 地址翻译完成 {address: '吉隆坡唐人街福朋喜来登酒店', translationCount: 2}
logger.js:470 [3:05:49 PM] [SUCCESS] 地址翻译完成 {address: '吉隆坡国际机场T1', translationCount: 2}
logger.js:473 [3:05:49 PM] [INFO] 🔍 完整数据模糊匹配: 吉隆坡唐人街福朋喜来登酒店 → Shangri-La Hotel 
logger.js:199 🔍 完整数据模糊匹配: 吉隆坡唐人街福朋喜来登酒店 → Shangri-La Hotel
logger.js:473 [3:05:49 PM] [INFO] 🔍 完整数据模糊匹配: 吉隆坡国际机场T1 → Shangri-La Hotel 
logger.js:199 🔍 完整数据模糊匹配: 吉隆坡国际机场T1 → Shangri-La Hotel
logger.js:470 [3:05:49 PM] [SUCCESS] 步骤 local_mapping 完成 {success: true, time: '21ms'}
logger.js:473 [3:05:49 PM] [INFO] 执行流水线步骤: maps_api_query 
logger.js:470 [3:05:49 PM] [SUCCESS] 步骤 local_mapping 完成 {success: true, time: '20ms'}
logger.js:473 [3:05:49 PM] [INFO] 执行流水线步骤: maps_api_query 
index.html:1 Access to fetch at 'https://maps.googleapis.com/maps/api/place/findplacefromtext/json?input=%E5%90%89%E9%9A%86%E5%9D%A1%E5%94%90%E4%BA%BA%E8%A1%97%E7%A6%8F%E6%9C%8B%E5%96%9C%E6%9D%A5%E7%99%BB%E9%85%92%E5%BA%97&key=AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s&inputtype=textquery&fields=place_id%2Cname%2Cformatted_address%2Cgeometry%2Ctypes&language=en' from origin 'null' has been blocked by CORS policy: No 'Access-Control-Allow-Origin' header is present on the requested resource.
google-maps-service.js:747  GET https://maps.googleapis.com/maps/api/place/findplacefromtext/json?input=%E5%90%89%E9%9A%86%E5%9D%A1%E5%94%90%E4%BA%BA%E8%A1%97%E7%A6%8F%E6%9C%8B%E5%96%9C%E6%9D%A5%E7%99%BB%E9%85%92%E5%BA%97&key=AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s&inputtype=textquery&fields=place_id%2Cname%2Cformatted_address%2Cgeometry%2Ctypes&language=en net::ERR_FAILED 200 (OK)
makeRequest @ google-maps-service.js:747
findPlaceFromText @ google-maps-service.js:131
executeMapsApiQuery @ address-pipeline-coordinator.js:444
executeStep @ address-pipeline-coordinator.js:353
executePipeline @ address-pipeline-coordinator.js:307
await in executePipeline
processAddress @ address-pipeline-coordinator.js:193
await in processAddress
(anonymous) @ form-manager.js:874
processAddressesAsync @ form-manager.js:872
(anonymous) @ form-manager.js:779
setTimeout
triggerAddressPipeline @ form-manager.js:778
fillFormFromData @ form-manager.js:741
updateOrderForm @ ui-state-manager.js:297
(anonymous) @ ui-state-manager.js:88
(anonymous) @ app-state.js:286
notify @ app-state.js:284
set @ app-state.js:232
update @ app-state.js:244
setCurrentOrder @ app-state.js:445
handleAnalysisResult @ realtime-analysis-manager.js:663
triggerRealtimeAnalysis @ realtime-analysis-manager.js:575
await in triggerRealtimeAnalysis
(anonymous) @ realtime-analysis-manager.js:403
setTimeout
handleRealtimeInput @ realtime-analysis-manager.js:402
logger.js:473 [3:05:49 PM] [WARN] API请求失败 {url: 'https://maps.googleapis.com/maps/api/place/findplacefromtext/json', error: 'Failed to fetch', category: 'unknown', retryCount: 0, maxRetries: 3}
logger.js:464 [3:05:49 PM] [ERROR] Places API 查找失败 {query: '吉隆坡唐人街福朋喜来登酒店', error: 'Failed to fetch'}
outputToConsole @ logger.js:464
log @ logger.js:384
findPlaceFromText @ google-maps-service.js:159
await in findPlaceFromText
executeMapsApiQuery @ address-pipeline-coordinator.js:444
executeStep @ address-pipeline-coordinator.js:353
executePipeline @ address-pipeline-coordinator.js:307
await in executePipeline
processAddress @ address-pipeline-coordinator.js:193
await in processAddress
(anonymous) @ form-manager.js:874
processAddressesAsync @ form-manager.js:872
(anonymous) @ form-manager.js:779
setTimeout
triggerAddressPipeline @ form-manager.js:778
fillFormFromData @ form-manager.js:741
updateOrderForm @ ui-state-manager.js:297
(anonymous) @ ui-state-manager.js:88
(anonymous) @ app-state.js:286
notify @ app-state.js:284
set @ app-state.js:232
update @ app-state.js:244
setCurrentOrder @ app-state.js:445
handleAnalysisResult @ realtime-analysis-manager.js:663
triggerRealtimeAnalysis @ realtime-analysis-manager.js:575
await in triggerRealtimeAnalysis
(anonymous) @ realtime-analysis-manager.js:403
setTimeout
handleRealtimeInput @ realtime-analysis-manager.js:402
index.html:1 Access to fetch at 'https://maps.googleapis.com/maps/api/place/findplacefromtext/json?input=%E5%90%89%E9%9A%86%E5%9D%A1%E5%9B%BD%E9%99%85%E6%9C%BA%E5%9C%BAT1&key=AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s&inputtype=textquery&fields=place_id%2Cname%2Cformatted_address%2Cgeometry%2Ctypes&language=en' from origin 'null' has been blocked by CORS policy: No 'Access-Control-Allow-Origin' header is present on the requested resource.
google-maps-service.js:747  GET https://maps.googleapis.com/maps/api/place/findplacefromtext/json?input=%E5%90%89%E9%9A%86%E5%9D%A1%E5%9B%BD%E9%99%85%E6%9C%BA%E5%9C%BAT1&key=AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s&inputtype=textquery&fields=place_id%2Cname%2Cformatted_address%2Cgeometry%2Ctypes&language=en net::ERR_FAILED 200 (OK)
makeRequest @ google-maps-service.js:747
findPlaceFromText @ google-maps-service.js:131
executeMapsApiQuery @ address-pipeline-coordinator.js:444
executeStep @ address-pipeline-coordinator.js:353
executePipeline @ address-pipeline-coordinator.js:307
await in executePipeline
processAddress @ address-pipeline-coordinator.js:193
await in processAddress
(anonymous) @ form-manager.js:874
processAddressesAsync @ form-manager.js:872
(anonymous) @ form-manager.js:779
setTimeout
triggerAddressPipeline @ form-manager.js:778
fillFormFromData @ form-manager.js:741
updateOrderForm @ ui-state-manager.js:297
(anonymous) @ ui-state-manager.js:88
(anonymous) @ app-state.js:286
notify @ app-state.js:284
set @ app-state.js:232
update @ app-state.js:244
setCurrentOrder @ app-state.js:445
handleAnalysisResult @ realtime-analysis-manager.js:663
triggerRealtimeAnalysis @ realtime-analysis-manager.js:575
await in triggerRealtimeAnalysis
(anonymous) @ realtime-analysis-manager.js:403
setTimeout
handleRealtimeInput @ realtime-analysis-manager.js:402
logger.js:473 [3:05:49 PM] [WARN] API请求失败 {url: 'https://maps.googleapis.com/maps/api/place/findplacefromtext/json', error: 'Failed to fetch', category: 'unknown', retryCount: 0, maxRetries: 3}
logger.js:464 [3:05:49 PM] [ERROR] Places API 查找失败 {query: '吉隆坡国际机场T1', error: 'Failed to fetch'}
outputToConsole @ logger.js:464
log @ logger.js:384
findPlaceFromText @ google-maps-service.js:159
await in findPlaceFromText
executeMapsApiQuery @ address-pipeline-coordinator.js:444
executeStep @ address-pipeline-coordinator.js:353
executePipeline @ address-pipeline-coordinator.js:307
await in executePipeline
processAddress @ address-pipeline-coordinator.js:193
await in processAddress
(anonymous) @ form-manager.js:874
processAddressesAsync @ form-manager.js:872
(anonymous) @ form-manager.js:779
setTimeout
triggerAddressPipeline @ form-manager.js:778
fillFormFromData @ form-manager.js:741
updateOrderForm @ ui-state-manager.js:297
(anonymous) @ ui-state-manager.js:88
(anonymous) @ app-state.js:286
notify @ app-state.js:284
set @ app-state.js:232
update @ app-state.js:244
setCurrentOrder @ app-state.js:445
handleAnalysisResult @ realtime-analysis-manager.js:663
triggerRealtimeAnalysis @ realtime-analysis-manager.js:575
await in triggerRealtimeAnalysis
(anonymous) @ realtime-analysis-manager.js:403
setTimeout
handleRealtimeInput @ realtime-analysis-manager.js:402
index.html:1 Access to fetch at 'https://maps.googleapis.com/maps/api/place/findplacefromtext/json?input=%E5%90%89%E9%9A%86%E5%9D%A1%E5%94%90%E4%BA%BA%E8%A1%97%E7%A6%8F%E6%9C%8B%E5%96%9C%E6%9D%A5%E7%99%BB%E9%85%92%E5%BA%97&key=AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s&inputtype=textquery&fields=place_id%2Cname%2Cformatted_address%2Ctypes&language=en' from origin 'null' has been blocked by CORS policy: No 'Access-Control-Allow-Origin' header is present on the requested resource.
google-maps-service.js:747  GET https://maps.googleapis.com/maps/api/place/findplacefromtext/json?input=%E5%90%89%E9%9A%86%E5%9D%A1%E5%94%90%E4%BA%BA%E8%A1%97%E7%A6%8F%E6%9C%8B%E5%96%9C%E6%9D%A5%E7%99%BB%E9%85%92%E5%BA%97&key=AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s&inputtype=textquery&fields=place_id%2Cname%2Cformatted_address%2Ctypes&language=en net::ERR_FAILED 200 (OK)
makeRequest @ google-maps-service.js:747
findPlaceFromText @ google-maps-service.js:131
standardizeWithPlacesApi @ address-translator.js:406
executeMapsApiQuery @ address-pipeline-coordinator.js:459
await in executeMapsApiQuery
executeStep @ address-pipeline-coordinator.js:353
executePipeline @ address-pipeline-coordinator.js:307
await in executePipeline
processAddress @ address-pipeline-coordinator.js:193
await in processAddress
(anonymous) @ form-manager.js:874
processAddressesAsync @ form-manager.js:872
(anonymous) @ form-manager.js:779
setTimeout
triggerAddressPipeline @ form-manager.js:778
fillFormFromData @ form-manager.js:741
updateOrderForm @ ui-state-manager.js:297
(anonymous) @ ui-state-manager.js:88
(anonymous) @ app-state.js:286
notify @ app-state.js:284
set @ app-state.js:232
update @ app-state.js:244
setCurrentOrder @ app-state.js:445
handleAnalysisResult @ realtime-analysis-manager.js:663
triggerRealtimeAnalysis @ realtime-analysis-manager.js:575
await in triggerRealtimeAnalysis
(anonymous) @ realtime-analysis-manager.js:403
setTimeout
handleRealtimeInput @ realtime-analysis-manager.js:402
logger.js:473 [3:05:49 PM] [WARN] API请求失败 {url: 'https://maps.googleapis.com/maps/api/place/findplacefromtext/json', error: 'Failed to fetch', category: 'unknown', retryCount: 0, maxRetries: 3}
logger.js:464 [3:05:49 PM] [ERROR] Places API 查找失败 {query: '吉隆坡唐人街福朋喜来登酒店', error: 'Failed to fetch'}
outputToConsole @ logger.js:464
log @ logger.js:384
findPlaceFromText @ google-maps-service.js:159
await in findPlaceFromText
standardizeWithPlacesApi @ address-translator.js:406
executeMapsApiQuery @ address-pipeline-coordinator.js:459
await in executeMapsApiQuery
executeStep @ address-pipeline-coordinator.js:353
executePipeline @ address-pipeline-coordinator.js:307
await in executePipeline
processAddress @ address-pipeline-coordinator.js:193
await in processAddress
(anonymous) @ form-manager.js:874
processAddressesAsync @ form-manager.js:872
(anonymous) @ form-manager.js:779
setTimeout
triggerAddressPipeline @ form-manager.js:778
fillFormFromData @ form-manager.js:741
updateOrderForm @ ui-state-manager.js:297
(anonymous) @ ui-state-manager.js:88
(anonymous) @ app-state.js:286
notify @ app-state.js:284
set @ app-state.js:232
update @ app-state.js:244
setCurrentOrder @ app-state.js:445
handleAnalysisResult @ realtime-analysis-manager.js:663
triggerRealtimeAnalysis @ realtime-analysis-manager.js:575
await in triggerRealtimeAnalysis
(anonymous) @ realtime-analysis-manager.js:403
setTimeout
handleRealtimeInput @ realtime-analysis-manager.js:402
logger.js:473 [3:05:49 PM] [WARN] 步骤 maps_api_query 完成 {success: false, time: '330ms'}
logger.js:473 [3:05:49 PM] [INFO] 执行流水线步骤: ai_translation 
index.html:1 Access to fetch at 'https://maps.googleapis.com/maps/api/place/findplacefromtext/json?input=%E5%90%89%E9%9A%86%E5%9D%A1%E5%9B%BD%E9%99%85%E6%9C%BA%E5%9C%BAT1&key=AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s&inputtype=textquery&fields=place_id%2Cname%2Cformatted_address%2Ctypes&language=en' from origin 'null' has been blocked by CORS policy: No 'Access-Control-Allow-Origin' header is present on the requested resource.
google-maps-service.js:747  GET https://maps.googleapis.com/maps/api/place/findplacefromtext/json?input=%E5%90%89%E9%9A%86%E5%9D%A1%E5%9B%BD%E9%99%85%E6%9C%BA%E5%9C%BAT1&key=AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s&inputtype=textquery&fields=place_id%2Cname%2Cformatted_address%2Ctypes&language=en net::ERR_FAILED 200 (OK)
makeRequest @ google-maps-service.js:747
findPlaceFromText @ google-maps-service.js:131
standardizeWithPlacesApi @ address-translator.js:406
executeMapsApiQuery @ address-pipeline-coordinator.js:459
await in executeMapsApiQuery
executeStep @ address-pipeline-coordinator.js:353
executePipeline @ address-pipeline-coordinator.js:307
await in executePipeline
processAddress @ address-pipeline-coordinator.js:193
await in processAddress
(anonymous) @ form-manager.js:874
processAddressesAsync @ form-manager.js:872
(anonymous) @ form-manager.js:779
setTimeout
triggerAddressPipeline @ form-manager.js:778
fillFormFromData @ form-manager.js:741
updateOrderForm @ ui-state-manager.js:297
(anonymous) @ ui-state-manager.js:88
(anonymous) @ app-state.js:286
notify @ app-state.js:284
set @ app-state.js:232
update @ app-state.js:244
setCurrentOrder @ app-state.js:445
handleAnalysisResult @ realtime-analysis-manager.js:663
triggerRealtimeAnalysis @ realtime-analysis-manager.js:575
await in triggerRealtimeAnalysis
(anonymous) @ realtime-analysis-manager.js:403
setTimeout
handleRealtimeInput @ realtime-analysis-manager.js:402
logger.js:473 [3:05:49 PM] [WARN] API请求失败 {url: 'https://maps.googleapis.com/maps/api/place/findplacefromtext/json', error: 'Failed to fetch', category: 'unknown', retryCount: 0, maxRetries: 3}
logger.js:464 [3:05:49 PM] [ERROR] Places API 查找失败 {query: '吉隆坡国际机场T1', error: 'Failed to fetch'}
outputToConsole @ logger.js:464
log @ logger.js:384
findPlaceFromText @ google-maps-service.js:159
await in findPlaceFromText
standardizeWithPlacesApi @ address-translator.js:406
executeMapsApiQuery @ address-pipeline-coordinator.js:459
await in executeMapsApiQuery
executeStep @ address-pipeline-coordinator.js:353
executePipeline @ address-pipeline-coordinator.js:307
await in executePipeline
processAddress @ address-pipeline-coordinator.js:193
await in processAddress
(anonymous) @ form-manager.js:874
processAddressesAsync @ form-manager.js:872
(anonymous) @ form-manager.js:779
setTimeout
triggerAddressPipeline @ form-manager.js:778
fillFormFromData @ form-manager.js:741
updateOrderForm @ ui-state-manager.js:297
(anonymous) @ ui-state-manager.js:88
(anonymous) @ app-state.js:286
notify @ app-state.js:284
set @ app-state.js:232
update @ app-state.js:244
setCurrentOrder @ app-state.js:445
handleAnalysisResult @ realtime-analysis-manager.js:663
triggerRealtimeAnalysis @ realtime-analysis-manager.js:575
await in triggerRealtimeAnalysis
(anonymous) @ realtime-analysis-manager.js:403
setTimeout
handleRealtimeInput @ realtime-analysis-manager.js:402
logger.js:473 [3:05:49 PM] [WARN] 步骤 maps_api_query 完成 {success: false, time: '334ms'}
logger.js:473 [3:05:49 PM] [INFO] 执行流水线步骤: ai_translation 
google-maps-service.js:747  GET https://kgsearch.googleapis.com/v1/entities:search?query=%E5%90%89%E9%9A%86%E5%9D%A1%E5%9B%BD%E9%99%85%E6%9C%BA%E5%9C%BAT1&key=AIzaSyA7dsSl_x-Jgj8XiEGruruc2mzlBDrWdfE&limit=5&languages=en%2Czh%2Cms&types=Place%2CLodgingBusiness%2CHotel 400 (Bad Request)
makeRequest @ google-maps-service.js:747
searchKnowledgeGraph @ google-maps-service.js:263
executeAiTranslation @ address-pipeline-coordinator.js:504
executeStep @ address-pipeline-coordinator.js:356
executePipeline @ address-pipeline-coordinator.js:307
await in executePipeline
processAddress @ address-pipeline-coordinator.js:193
await in processAddress
(anonymous) @ form-manager.js:874
processAddressesAsync @ form-manager.js:872
(anonymous) @ form-manager.js:779
setTimeout
triggerAddressPipeline @ form-manager.js:778
fillFormFromData @ form-manager.js:741
updateOrderForm @ ui-state-manager.js:297
(anonymous) @ ui-state-manager.js:88
(anonymous) @ app-state.js:286
notify @ app-state.js:284
set @ app-state.js:232
update @ app-state.js:244
setCurrentOrder @ app-state.js:445
handleAnalysisResult @ realtime-analysis-manager.js:663
triggerRealtimeAnalysis @ realtime-analysis-manager.js:575
await in triggerRealtimeAnalysis
(anonymous) @ realtime-analysis-manager.js:403
setTimeout
handleRealtimeInput @ realtime-analysis-manager.js:402
logger.js:473 [3:05:51 PM] [WARN] API请求失败 {url: 'https://kgsearch.googleapis.com/v1/entities:search', error: 'HTTP 400: ', category: 'client_error', retryCount: 0, maxRetries: 3}
logger.js:464 [3:05:51 PM] [ERROR] Knowledge Graph API 搜索失败 {query: '吉隆坡国际机场T1', error: 'HTTP 400: '}
outputToConsole @ logger.js:464
log @ logger.js:384
searchKnowledgeGraph @ google-maps-service.js:291
await in searchKnowledgeGraph
executeAiTranslation @ address-pipeline-coordinator.js:504
executeStep @ address-pipeline-coordinator.js:356
executePipeline @ address-pipeline-coordinator.js:307
await in executePipeline
processAddress @ address-pipeline-coordinator.js:193
await in processAddress
(anonymous) @ form-manager.js:874
processAddressesAsync @ form-manager.js:872
(anonymous) @ form-manager.js:779
setTimeout
triggerAddressPipeline @ form-manager.js:778
fillFormFromData @ form-manager.js:741
updateOrderForm @ ui-state-manager.js:297
(anonymous) @ ui-state-manager.js:88
(anonymous) @ app-state.js:286
notify @ app-state.js:284
set @ app-state.js:232
update @ app-state.js:244
setCurrentOrder @ app-state.js:445
handleAnalysisResult @ realtime-analysis-manager.js:663
triggerRealtimeAnalysis @ realtime-analysis-manager.js:575
await in triggerRealtimeAnalysis
(anonymous) @ realtime-analysis-manager.js:403
setTimeout
handleRealtimeInput @ realtime-analysis-manager.js:402
google-maps-service.js:747  GET https://kgsearch.googleapis.com/v1/entities:search?query=%E5%90%89%E9%9A%86%E5%9D%A1%E5%94%90%E4%BA%BA%E8%A1%97%E7%A6%8F%E6%9C%8B%E5%96%9C%E6%9D%A5%E7%99%BB%E9%85%92%E5%BA%97&key=AIzaSyA7dsSl_x-Jgj8XiEGruruc2mzlBDrWdfE&limit=5&languages=en%2Czh%2Cms&types=Place%2CLodgingBusiness%2CHotel 400 (Bad Request)
makeRequest @ google-maps-service.js:747
searchKnowledgeGraph @ google-maps-service.js:263
executeAiTranslation @ address-pipeline-coordinator.js:504
executeStep @ address-pipeline-coordinator.js:356
executePipeline @ address-pipeline-coordinator.js:307
await in executePipeline
processAddress @ address-pipeline-coordinator.js:193
await in processAddress
(anonymous) @ form-manager.js:874
processAddressesAsync @ form-manager.js:872
(anonymous) @ form-manager.js:779
setTimeout
triggerAddressPipeline @ form-manager.js:778
fillFormFromData @ form-manager.js:741
updateOrderForm @ ui-state-manager.js:297
(anonymous) @ ui-state-manager.js:88
(anonymous) @ app-state.js:286
notify @ app-state.js:284
set @ app-state.js:232
update @ app-state.js:244
setCurrentOrder @ app-state.js:445
handleAnalysisResult @ realtime-analysis-manager.js:663
triggerRealtimeAnalysis @ realtime-analysis-manager.js:575
await in triggerRealtimeAnalysis
(anonymous) @ realtime-analysis-manager.js:403
setTimeout
handleRealtimeInput @ realtime-analysis-manager.js:402
logger.js:473 [3:05:51 PM] [WARN] API请求失败 {url: 'https://kgsearch.googleapis.com/v1/entities:search', error: 'HTTP 400: ', category: 'client_error', retryCount: 0, maxRetries: 3}
logger.js:464 [3:05:51 PM] [ERROR] Knowledge Graph API 搜索失败 {query: '吉隆坡唐人街福朋喜来登酒店', error: 'HTTP 400: '}
outputToConsole @ logger.js:464
log @ logger.js:384
searchKnowledgeGraph @ google-maps-service.js:291
await in searchKnowledgeGraph
executeAiTranslation @ address-pipeline-coordinator.js:504
executeStep @ address-pipeline-coordinator.js:356
executePipeline @ address-pipeline-coordinator.js:307
await in executePipeline
processAddress @ address-pipeline-coordinator.js:193
await in processAddress
(anonymous) @ form-manager.js:874
processAddressesAsync @ form-manager.js:872
(anonymous) @ form-manager.js:779
setTimeout
triggerAddressPipeline @ form-manager.js:778
fillFormFromData @ form-manager.js:741
updateOrderForm @ ui-state-manager.js:297
(anonymous) @ ui-state-manager.js:88
(anonymous) @ app-state.js:286
notify @ app-state.js:284
set @ app-state.js:232
update @ app-state.js:244
setCurrentOrder @ app-state.js:445
handleAnalysisResult @ realtime-analysis-manager.js:663
triggerRealtimeAnalysis @ realtime-analysis-manager.js:575
await in triggerRealtimeAnalysis
(anonymous) @ realtime-analysis-manager.js:403
setTimeout
handleRealtimeInput @ realtime-analysis-manager.js:402
google-maps-service.js:327  POST https://translation.googleapis.com/language/translate/v2 400 (Bad Request)
translateText @ google-maps-service.js:327
executeAiTranslation @ address-pipeline-coordinator.js:510
await in executeAiTranslation
executeStep @ address-pipeline-coordinator.js:356
executePipeline @ address-pipeline-coordinator.js:307
await in executePipeline
processAddress @ address-pipeline-coordinator.js:193
await in processAddress
(anonymous) @ form-manager.js:874
processAddressesAsync @ form-manager.js:872
(anonymous) @ form-manager.js:779
setTimeout
triggerAddressPipeline @ form-manager.js:778
fillFormFromData @ form-manager.js:741
updateOrderForm @ ui-state-manager.js:297
(anonymous) @ ui-state-manager.js:88
(anonymous) @ app-state.js:286
notify @ app-state.js:284
set @ app-state.js:232
update @ app-state.js:244
setCurrentOrder @ app-state.js:445
handleAnalysisResult @ realtime-analysis-manager.js:663
triggerRealtimeAnalysis @ realtime-analysis-manager.js:575
await in triggerRealtimeAnalysis
(anonymous) @ realtime-analysis-manager.js:403
setTimeout
handleRealtimeInput @ realtime-analysis-manager.js:402
logger.js:464 [3:05:51 PM] [ERROR] Translation API 翻译失败 {text: '吉隆坡国际机场T1', error: 'HTTP 400: '}
outputToConsole @ logger.js:464
log @ logger.js:384
translateText @ google-maps-service.js:371
await in translateText
executeAiTranslation @ address-pipeline-coordinator.js:510
await in executeAiTranslation
executeStep @ address-pipeline-coordinator.js:356
executePipeline @ address-pipeline-coordinator.js:307
await in executePipeline
processAddress @ address-pipeline-coordinator.js:193
await in processAddress
(anonymous) @ form-manager.js:874
processAddressesAsync @ form-manager.js:872
(anonymous) @ form-manager.js:779
setTimeout
triggerAddressPipeline @ form-manager.js:778
fillFormFromData @ form-manager.js:741
updateOrderForm @ ui-state-manager.js:297
(anonymous) @ ui-state-manager.js:88
(anonymous) @ app-state.js:286
notify @ app-state.js:284
set @ app-state.js:232
update @ app-state.js:244
setCurrentOrder @ app-state.js:445
handleAnalysisResult @ realtime-analysis-manager.js:663
triggerRealtimeAnalysis @ realtime-analysis-manager.js:575
await in triggerRealtimeAnalysis
(anonymous) @ realtime-analysis-manager.js:403
setTimeout
handleRealtimeInput @ realtime-analysis-manager.js:402
google-maps-service.js:327  POST https://translation.googleapis.com/language/translate/v2 400 (Bad Request)
translateText @ google-maps-service.js:327
executeAiTranslation @ address-pipeline-coordinator.js:510
await in executeAiTranslation
executeStep @ address-pipeline-coordinator.js:356
executePipeline @ address-pipeline-coordinator.js:307
await in executePipeline
processAddress @ address-pipeline-coordinator.js:193
await in processAddress
(anonymous) @ form-manager.js:874
processAddressesAsync @ form-manager.js:872
(anonymous) @ form-manager.js:779
setTimeout
triggerAddressPipeline @ form-manager.js:778
fillFormFromData @ form-manager.js:741
updateOrderForm @ ui-state-manager.js:297
(anonymous) @ ui-state-manager.js:88
(anonymous) @ app-state.js:286
notify @ app-state.js:284
set @ app-state.js:232
update @ app-state.js:244
setCurrentOrder @ app-state.js:445
handleAnalysisResult @ realtime-analysis-manager.js:663
triggerRealtimeAnalysis @ realtime-analysis-manager.js:575
await in triggerRealtimeAnalysis
(anonymous) @ realtime-analysis-manager.js:403
setTimeout
handleRealtimeInput @ realtime-analysis-manager.js:402
logger.js:464 [3:05:51 PM] [ERROR] Translation API 翻译失败 {text: '吉隆坡唐人街福朋喜来登酒店', error: 'HTTP 400: '}
outputToConsole @ logger.js:464
log @ logger.js:384
translateText @ google-maps-service.js:371
await in translateText
executeAiTranslation @ address-pipeline-coordinator.js:510
await in executeAiTranslation
executeStep @ address-pipeline-coordinator.js:356
executePipeline @ address-pipeline-coordinator.js:307
await in executePipeline
processAddress @ address-pipeline-coordinator.js:193
await in processAddress
(anonymous) @ form-manager.js:874
processAddressesAsync @ form-manager.js:872
(anonymous) @ form-manager.js:779
setTimeout
triggerAddressPipeline @ form-manager.js:778
fillFormFromData @ form-manager.js:741
updateOrderForm @ ui-state-manager.js:297
(anonymous) @ ui-state-manager.js:88
(anonymous) @ app-state.js:286
notify @ app-state.js:284
set @ app-state.js:232
update @ app-state.js:244
setCurrentOrder @ app-state.js:445
handleAnalysisResult @ realtime-analysis-manager.js:663
triggerRealtimeAnalysis @ realtime-analysis-manager.js:575
await in triggerRealtimeAnalysis
(anonymous) @ realtime-analysis-manager.js:403
setTimeout
handleRealtimeInput @ realtime-analysis-manager.js:402
google-maps-service.js:747  GET https://kgsearch.googleapis.com/v1/entities:search?query=%E5%90%89%E9%9A%86%E5%9D%A1%E5%9B%BD%E9%99%85%E6%9C%BA%E5%9C%BAT1&key=AIzaSyA7dsSl_x-Jgj8XiEGruruc2mzlBDrWdfE&limit=3&languages=en%2Czh%2Cms&types=Place%2CLodgingBusiness%2CHotel 400 (Bad Request)
makeRequest @ google-maps-service.js:747
searchKnowledgeGraph @ google-maps-service.js:263
standardizeWithKnowledgeGraph @ address-translator.js:511
executeAiTranslation @ address-pipeline-coordinator.js:522
await in executeAiTranslation
executeStep @ address-pipeline-coordinator.js:356
executePipeline @ address-pipeline-coordinator.js:307
await in executePipeline
processAddress @ address-pipeline-coordinator.js:193
await in processAddress
(anonymous) @ form-manager.js:874
processAddressesAsync @ form-manager.js:872
(anonymous) @ form-manager.js:779
setTimeout
triggerAddressPipeline @ form-manager.js:778
fillFormFromData @ form-manager.js:741
updateOrderForm @ ui-state-manager.js:297
(anonymous) @ ui-state-manager.js:88
(anonymous) @ app-state.js:286
notify @ app-state.js:284
set @ app-state.js:232
update @ app-state.js:244
setCurrentOrder @ app-state.js:445
handleAnalysisResult @ realtime-analysis-manager.js:663
triggerRealtimeAnalysis @ realtime-analysis-manager.js:575
await in triggerRealtimeAnalysis
(anonymous) @ realtime-analysis-manager.js:403
setTimeout
handleRealtimeInput @ realtime-analysis-manager.js:402
logger.js:473 [3:05:52 PM] [WARN] API请求失败 {url: 'https://kgsearch.googleapis.com/v1/entities:search', error: 'HTTP 400: ', category: 'client_error', retryCount: 0, maxRetries: 3}
logger.js:464 [3:05:52 PM] [ERROR] Knowledge Graph API 搜索失败 {query: '吉隆坡国际机场T1', error: 'HTTP 400: '}
outputToConsole @ logger.js:464
log @ logger.js:384
searchKnowledgeGraph @ google-maps-service.js:291
await in searchKnowledgeGraph
standardizeWithKnowledgeGraph @ address-translator.js:511
executeAiTranslation @ address-pipeline-coordinator.js:522
await in executeAiTranslation
executeStep @ address-pipeline-coordinator.js:356
executePipeline @ address-pipeline-coordinator.js:307
await in executePipeline
processAddress @ address-pipeline-coordinator.js:193
await in processAddress
(anonymous) @ form-manager.js:874
processAddressesAsync @ form-manager.js:872
(anonymous) @ form-manager.js:779
setTimeout
triggerAddressPipeline @ form-manager.js:778
fillFormFromData @ form-manager.js:741
updateOrderForm @ ui-state-manager.js:297
(anonymous) @ ui-state-manager.js:88
(anonymous) @ app-state.js:286
notify @ app-state.js:284
set @ app-state.js:232
update @ app-state.js:244
setCurrentOrder @ app-state.js:445
handleAnalysisResult @ realtime-analysis-manager.js:663
triggerRealtimeAnalysis @ realtime-analysis-manager.js:575
await in triggerRealtimeAnalysis
(anonymous) @ realtime-analysis-manager.js:403
setTimeout
handleRealtimeInput @ realtime-analysis-manager.js:402
google-maps-service.js:747  GET https://kgsearch.googleapis.com/v1/entities:search?query=%E5%90%89%E9%9A%86%E5%9D%A1%E5%94%90%E4%BA%BA%E8%A1%97%E7%A6%8F%E6%9C%8B%E5%96%9C%E6%9D%A5%E7%99%BB%E9%85%92%E5%BA%97&key=AIzaSyA7dsSl_x-Jgj8XiEGruruc2mzlBDrWdfE&limit=3&languages=en%2Czh%2Cms&types=Place%2CLodgingBusiness%2CHotel 400 (Bad Request)
makeRequest @ google-maps-service.js:747
searchKnowledgeGraph @ google-maps-service.js:263
standardizeWithKnowledgeGraph @ address-translator.js:511
executeAiTranslation @ address-pipeline-coordinator.js:522
await in executeAiTranslation
executeStep @ address-pipeline-coordinator.js:356
executePipeline @ address-pipeline-coordinator.js:307
await in executePipeline
processAddress @ address-pipeline-coordinator.js:193
await in processAddress
(anonymous) @ form-manager.js:874
processAddressesAsync @ form-manager.js:872
(anonymous) @ form-manager.js:779
setTimeout
triggerAddressPipeline @ form-manager.js:778
fillFormFromData @ form-manager.js:741
updateOrderForm @ ui-state-manager.js:297
(anonymous) @ ui-state-manager.js:88
(anonymous) @ app-state.js:286
notify @ app-state.js:284
set @ app-state.js:232
update @ app-state.js:244
setCurrentOrder @ app-state.js:445
handleAnalysisResult @ realtime-analysis-manager.js:663
triggerRealtimeAnalysis @ realtime-analysis-manager.js:575
await in triggerRealtimeAnalysis
(anonymous) @ realtime-analysis-manager.js:403
setTimeout
handleRealtimeInput @ realtime-analysis-manager.js:402
logger.js:473 [3:05:52 PM] [WARN] API请求失败 {url: 'https://kgsearch.googleapis.com/v1/entities:search', error: 'HTTP 400: ', category: 'client_error', retryCount: 0, maxRetries: 3}
logger.js:464 [3:05:52 PM] [ERROR] Knowledge Graph API 搜索失败 {query: '吉隆坡唐人街福朋喜来登酒店', error: 'HTTP 400: '}
outputToConsole @ logger.js:464
log @ logger.js:384
searchKnowledgeGraph @ google-maps-service.js:291
await in searchKnowledgeGraph
standardizeWithKnowledgeGraph @ address-translator.js:511
executeAiTranslation @ address-pipeline-coordinator.js:522
await in executeAiTranslation
executeStep @ address-pipeline-coordinator.js:356
executePipeline @ address-pipeline-coordinator.js:307
await in executePipeline
processAddress @ address-pipeline-coordinator.js:193
await in processAddress
(anonymous) @ form-manager.js:874
processAddressesAsync @ form-manager.js:872
(anonymous) @ form-manager.js:779
setTimeout
triggerAddressPipeline @ form-manager.js:778
fillFormFromData @ form-manager.js:741
updateOrderForm @ ui-state-manager.js:297
(anonymous) @ ui-state-manager.js:88
(anonymous) @ app-state.js:286
notify @ app-state.js:284
set @ app-state.js:232
update @ app-state.js:244
setCurrentOrder @ app-state.js:445
handleAnalysisResult @ realtime-analysis-manager.js:663
triggerRealtimeAnalysis @ realtime-analysis-manager.js:575
await in triggerRealtimeAnalysis
(anonymous) @ realtime-analysis-manager.js:403
setTimeout
handleRealtimeInput @ realtime-analysis-manager.js:402
google-maps-service.js:327  POST https://translation.googleapis.com/language/translate/v2 400 (Bad Request)
translateText @ google-maps-service.js:327
standardizeWithTranslationApi @ address-translator.js:567
executeAiTranslation @ address-pipeline-coordinator.js:530
await in executeAiTranslation
executeStep @ address-pipeline-coordinator.js:356
executePipeline @ address-pipeline-coordinator.js:307
await in executePipeline
processAddress @ address-pipeline-coordinator.js:193
await in processAddress
(anonymous) @ form-manager.js:874
processAddressesAsync @ form-manager.js:872
(anonymous) @ form-manager.js:779
setTimeout
triggerAddressPipeline @ form-manager.js:778
fillFormFromData @ form-manager.js:741
updateOrderForm @ ui-state-manager.js:297
(anonymous) @ ui-state-manager.js:88
(anonymous) @ app-state.js:286
notify @ app-state.js:284
set @ app-state.js:232
update @ app-state.js:244
setCurrentOrder @ app-state.js:445
handleAnalysisResult @ realtime-analysis-manager.js:663
triggerRealtimeAnalysis @ realtime-analysis-manager.js:575
await in triggerRealtimeAnalysis
(anonymous) @ realtime-analysis-manager.js:403
setTimeout
handleRealtimeInput @ realtime-analysis-manager.js:402
logger.js:464 [3:05:52 PM] [ERROR] Translation API 翻译失败 {text: '吉隆坡国际机场T1', error: 'HTTP 400: '}
outputToConsole @ logger.js:464
log @ logger.js:384
translateText @ google-maps-service.js:371
await in translateText
standardizeWithTranslationApi @ address-translator.js:567
executeAiTranslation @ address-pipeline-coordinator.js:530
await in executeAiTranslation
executeStep @ address-pipeline-coordinator.js:356
executePipeline @ address-pipeline-coordinator.js:307
await in executePipeline
processAddress @ address-pipeline-coordinator.js:193
await in processAddress
(anonymous) @ form-manager.js:874
processAddressesAsync @ form-manager.js:872
(anonymous) @ form-manager.js:779
setTimeout
triggerAddressPipeline @ form-manager.js:778
fillFormFromData @ form-manager.js:741
updateOrderForm @ ui-state-manager.js:297
(anonymous) @ ui-state-manager.js:88
(anonymous) @ app-state.js:286
notify @ app-state.js:284
set @ app-state.js:232
update @ app-state.js:244
setCurrentOrder @ app-state.js:445
handleAnalysisResult @ realtime-analysis-manager.js:663
triggerRealtimeAnalysis @ realtime-analysis-manager.js:575
await in triggerRealtimeAnalysis
(anonymous) @ realtime-analysis-manager.js:403
setTimeout
handleRealtimeInput @ realtime-analysis-manager.js:402
logger.js:473 [3:05:52 PM] [WARN] 步骤 ai_translation 完成 {success: false, time: '3096ms'}
logger.js:470 [3:05:52 PM] [SUCCESS] 地址处理完成 {address: '吉隆坡国际机场T1', success: true, processingTime: '3467ms'}
logger.js:470 [3:05:52 PM] [SUCCESS] 地址字段已更新 {fieldName: 'dropoff', oldValue: '吉隆坡国际机场T1', newValue: 'Shangri-La Hotel', source: 'local_data'}
google-maps-service.js:327  POST https://translation.googleapis.com/language/translate/v2 400 (Bad Request)
translateText @ google-maps-service.js:327
standardizeWithTranslationApi @ address-translator.js:567
executeAiTranslation @ address-pipeline-coordinator.js:530
await in executeAiTranslation
executeStep @ address-pipeline-coordinator.js:356
executePipeline @ address-pipeline-coordinator.js:307
await in executePipeline
processAddress @ address-pipeline-coordinator.js:193
await in processAddress
(anonymous) @ form-manager.js:874
processAddressesAsync @ form-manager.js:872
(anonymous) @ form-manager.js:779
setTimeout
triggerAddressPipeline @ form-manager.js:778
fillFormFromData @ form-manager.js:741
updateOrderForm @ ui-state-manager.js:297
(anonymous) @ ui-state-manager.js:88
(anonymous) @ app-state.js:286
notify @ app-state.js:284
set @ app-state.js:232
update @ app-state.js:244
setCurrentOrder @ app-state.js:445
handleAnalysisResult @ realtime-analysis-manager.js:663
triggerRealtimeAnalysis @ realtime-analysis-manager.js:575
await in triggerRealtimeAnalysis
(anonymous) @ realtime-analysis-manager.js:403
setTimeout
handleRealtimeInput @ realtime-analysis-manager.js:402
logger.js:464 [3:05:52 PM] [ERROR] Translation API 翻译失败 {text: '吉隆坡唐人街福朋喜来登酒店', error: 'HTTP 400: '}
outputToConsole @ logger.js:464
log @ logger.js:384
translateText @ google-maps-service.js:371
await in translateText
standardizeWithTranslationApi @ address-translator.js:567
executeAiTranslation @ address-pipeline-coordinator.js:530
await in executeAiTranslation
executeStep @ address-pipeline-coordinator.js:356
executePipeline @ address-pipeline-coordinator.js:307
await in executePipeline
processAddress @ address-pipeline-coordinator.js:193
await in processAddress
(anonymous) @ form-manager.js:874
processAddressesAsync @ form-manager.js:872
(anonymous) @ form-manager.js:779
setTimeout
triggerAddressPipeline @ form-manager.js:778
fillFormFromData @ form-manager.js:741
updateOrderForm @ ui-state-manager.js:297
(anonymous) @ ui-state-manager.js:88
(anonymous) @ app-state.js:286
notify @ app-state.js:284
set @ app-state.js:232
update @ app-state.js:244
setCurrentOrder @ app-state.js:445
handleAnalysisResult @ realtime-analysis-manager.js:663
triggerRealtimeAnalysis @ realtime-analysis-manager.js:575
await in triggerRealtimeAnalysis
(anonymous) @ realtime-analysis-manager.js:403
setTimeout
handleRealtimeInput @ realtime-analysis-manager.js:402
logger.js:473 [3:05:52 PM] [WARN] 步骤 ai_translation 完成 {success: false, time: '3149ms'}
logger.js:470 [3:05:52 PM] [SUCCESS] 地址处理完成 {address: '吉隆坡唐人街福朋喜来登酒店', success: true, processingTime: '3517ms'}
logger.js:470 [3:05:52 PM] [SUCCESS] 地址字段已更新 {fieldName: 'pickup', oldValue: '吉隆坡唐人街福朋喜来登酒店', newValue: 'Shangri-La Hotel', source: 'local_data'}
logger.js:470 [3:05:52 PM] [SUCCESS] 地址处理流水线完成 {successCount: 2, totalCount: 2, successRate: '100%'}
