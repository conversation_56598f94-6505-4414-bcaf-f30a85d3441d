/**
 * ============================================================================
 * 🚀 核心业务流程 - 提示词构建器 (子层实现)
 * ============================================================================
 *
 * @fileoverview 提示词构建器 - 子层实现
 * @description 负责渠道策略文件调取和提示词组合，不调用Gemini API
 * 
 * @businessFlow 渠道策略文件调取和提示词组合
 * 在核心业务流程中的位置：
 * 输入内容 → 本地渠道特征检测
 *     ↓
 * A1. 无渠道特征 → 【当前文件职责】使用通用提示词 - 本地处理
 * A2. 有渠道特征 → 【当前文件职责】调取渠道策略文件提示词片段 → 组合提示词 - 本地处理
 *     ↓
 * 发送Gemini API (gemini-caller.js) → 结果处理 → 订单管理
 *
 * @architecture Child Layer (子层) - 本地处理实现
 * - 职责：提示词组合的具体实现
 * - 原则：专注单一功能，不依赖其他子层
 * - 接口：为母层提供提示词构建服务
 *
 * @dependencies 依赖关系
 * 上游依赖：
 * - controllers/business-flow-controller.js (母层控制器调用)
 * - strategies/fliggy-ota-strategy.js (Fliggy策略文件)
 * - strategies/jingge-ota-strategy.js (JingGe策略文件)
 * 下游依赖：无（底层实现）
 *
 * @localProcessing 本地处理职责（核心功能）
 * - 🟢 调取渠道策略文件的字段片段提示词
 * - 🟢 组合基础提示词和渠道专属提示词
 * - 🟢 处理无渠道特征的通用提示词
 * - 🟢 提示词模板管理和缓存
 *
 * @remoteProcessing 远程处理职责
 * - ❌ 无（纯本地处理模块）
 *
 * @compatibility 兼容性保证
 * - 保持现有渠道策略文件接口不变
 * - 兼容FliggyOTAStrategy.getFieldPromptSnippets()
 * - 兼容JingGeOTAStrategy.getFieldPromptSnippets()
 *
 * @refactoringConstraints 重构约束
 * - ✅ 严格保持现有渠道策略文件不变
 * - ✅ 不能调用Gemini API（严格本地处理）
 * - ✅ 不能依赖其他子层
 * - ✅ 必须保持策略文件调用方式不变
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-08-09
 * @lastModified 2025-08-09
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};

(function() {
    'use strict';

    /**
     * 提示词构建器 - 子层实现
     */
    class PromptBuilder {
        constructor() {
            this.logger = this.getLogger();
            
            // 基础提示词模板（对齐GoMyHire API与本地表单映射；来源：backup/js/gemini-service.js 核心规则，已裁剪与重命名以兼容 result-processor） // @UTIL 模板定义
            // 外部依赖分析：与渠道策略片段(ota/ota_price/car_type_id)互补；不改策略文件，仅在此统一基础字段规范 // 依赖说明
            // 全局变量声明记录：无 // 记录
            // 初始化流程说明：在构造器中加载，用于 buildPrompt 组合“基础提示词 + 渠道片段 + 原始输入” // 初始化说明
            this.basePromptTemplate = `
你是一个专为GoMyHire用车服务订单解析的智能引擎。请严格按照以下“输出契约”和“业务规则”解析输入文本，并返回标准化JSON。

【输出契约】
- 单订单：返回单个JSON对象（不是数组、无Markdown代码块、无额外解释文本）。
- 多订单：返回一个JSON对象，结构如下：
  {
    "isMultiOrder": true,
    "orderCount": <number>,
    "orders": [ { ...单订单对象... }, ... ],
    "confidence": <number|null>
  }

【字段定义（snake_case；与GoMyHire API一致；未知请填null；字段必须全部出现）】
{
  "customer_name": "string|null",
  "customer_contact": "string|null",
  "customer_email": "string|null",
  "ota": "string|null",
  "ota_reference_number": "string|null",
  "flight_info": "string|null",
  "departure_time": "string (HH:MM)|null",
  "arrival_time": "string (HH:MM)|null",
  "flight_type": "string ('Arrival'|'Departure')|null",
  "date": "string (YYYY-MM-DD)|null",
  "time": "string (HH:MM,24h)|null",
  "pickup": "string|null",
  "destination": "string|null",
  "passenger_number": "number|null",
  "luggage_number": "number|null",
  "sub_category_id": "number in {2,3,4}|null",
  "car_type_id": "number|null",        // 仅当文本明确给出车型/ID时填写，否则返回null（避免与本地智能选择逻辑冲突）
  "driving_region_id": "number|null",   // 例如 1:KL/Selangor, 2:Penang, 3:Johor, 4:Sabah, 5:Singapore, 12:Malacca
  "baby_chair": "boolean|null",
  "tour_guide": "boolean|null",
  "meet_and_greet": "boolean|null",
  "needs_paging_service": "boolean|null",
  "ota_price": "number|null",
  "currency": "string in {'MYR','USD','CNY'}|null",
  "extra_requirement": "string|null"
}

【强一致性输出指令】
- 单订单必须输出“单个JSON对象”，绝不能输出数组或包含Markdown代码块。
- 从以下 Skeleton 开始填写，保留所有键；未知一律填 null；严禁新增未在“字段定义”中出现的键：
Skeleton:
{
    "customer_name": null,
    "customer_contact": null,
    "customer_email": null,
    "ota": null,
    "ota_reference_number": null,
    "flight_info": null,
    "departure_time": null,
    "arrival_time": null,
    "flight_type": null,
    "date": null,
    "time": null,
    "pickup": null,
    "destination": null,
    "passenger_number": null,
    "luggage_number": null,
    "sub_category_id": null,
    "car_type_id": null,
    "driving_region_id": null,
    "baby_chair": null,
    "tour_guide": null,
    "meet_and_greet": null,
    "needs_paging_service": null,
    "ota_price": null,
    "currency": null,
    "extra_requirement": null
}

（务必以该 Skeleton 为基础进行填充，保持所有键存在，且不添加任何其他键。）

【多订单识别】
- 如文本存在多个独立订单（编号、分隔线、明显段落等），必须返回“多订单对象”结构。
- orders 数组中的每个元素都必须是完整的“单订单对象”，字段齐全（未知填null）。

【航班信息规则】
- 航班号：2-3位航空公司代码 + 1-4位数字，可含后缀字母/连字符/分段（如 MH123、CZ351B、MH-123、SQ807/123、9W123）。
- flight_type：到达→'Arrival'（抵达/到达/降落/arrival）；出发→'Departure'（出发/起飞/departure）。
- 时间：arrival_time / departure_time 统一用24小时制 HH:MM。

【服务类型 sub_category_id】
1) 若包含“包车/charter/全天/day tour/多小时/半日游/景点游”→ 4（包车）
2) 若 pickup_location 是机场且 dropoff_location 非机场 → 2（接机）
3) 若 dropoff_location 是机场且 pickup_location 非机场 → 3（送机）
4) 若 flight_type 是 'Arrival' → 2；若为 'Departure' → 3
5) 关键词：接机/airport pickup/从机场 → 2；送机/到机场/airport dropoff → 3
6) 均无法判断时，默认 2（接机）。

【时间与日期格式化】
- pickup_date 使用 YYYY-MM-DD；**必须从订单文本中提取具体日期**；支持"今天/明天/后天/周X/下周X"等相对日期换算到具体日期（以当前日期为基准）。
- 若文本包含如"2025-08-11"等完整日期格式，直接使用；若为相对日期如"明天"，需计算具体日期。
- pickup_time 使用 24h HH:MM；支持"上午/下午/AM/PM"等格式转换。

【价格与货币】
- 提取纯数字作为 ota_price；识别并标准化 currency 为 'MYR'/'USD'/'CNY'（如 RM、USD、$、人民币、CNY、￥、令吉/马币 等）。

【举牌/服务布尔值】
- 识别 meet & greet 相关关键词（举牌/迎接/meet and greet/paging）并设置 meet_and_greet 与 needs_paging_service 的布尔值。

【车型 car_type_id】
- 仅当文本中明确给出车型名称或ID且能唯一映射时填写具体 ID；否则返回 null（系统已有本地智能选择逻辑）。

【地点标准化（简要）】
- 常见机场/酒店请标准化：
  KLIA/KUL/吉隆坡国际机场 → "Kuala Lumpur International Airport (KLIA1)"
  KLIA2/吉隆坡第二国际机场 → "Kuala Lumpur International Airport 2 (KLIA2)"
  Changi/SIN/樟宜机场/新加坡机场 → "Singapore Changi Airport"
  SZB/梳邦机场/苏丹阿都阿兹沙机场 → "Sultan Abdul Aziz Shah Airport (Subang Airport)"
  PEN/槟城机场/槟城国际机场 → "Penang International Airport"
  BKI/亚庇机场/哥打京那巴鲁国际机场 → "Kota Kinabalu International Airport"
  JHB/新山机场/士乃国际机场 → "Senai International Airport (Johor Bahru)"
  TWU/斗湖机场 → "Tawau Airport"
- 若不确定，返回用户原始描述，不要猜测。
- 【精细约束】：
  **重要：pickup 和 destination 字段中的地点名称必须统一返回英文格式，严禁返回中文地名。**
  1. 地区/城市/机场/酒店等地名，优先翻译为官方英文全称（如有官方译名，务必采用官方标准）。
  2. 若为机场、火车站、酒店等地标，需补全全称（如"KLIA"需补全为"Kuala Lumpur International Airport (KLIA1)"）。
  3. 中文、英文、拼音、缩写等多种写法均需标准化为唯一官方英文全称。
  4. 若为地区（如"槟城"、"Penang"），统一翻译为官方英文地名（如"Penang"）。
  5. 若为酒店，尽量补全品牌及官方英文名称（如"香格里拉酒店"→"Shangri-La Hotel"）。
  6. 若为新加坡、马来西亚等跨国地名，需明确国家归属（如"新加坡樟宜机场"→"Singapore Changi Airport"）。
  7. 若遇到不常见或模糊地名，保留原文，不要自行猜测或创造译名。
  8. 所有标准化后地名，均应为英文（如有必要可在括号内补充原文）。
  9. 连锁酒店如出现"品牌+区域"或"区域+品牌"顺序（如"吉隆坡希尔顿"或"Hilton Kuala Lumpur"），均需标准化为"Hilton Kuala Lumpur"官方英文格式，品牌在前，区域在后，且仅保留一个标准顺序。

【驾驶区域 driving_region_id 规则（由模型判断，不在本地计算）】
- **重要：必须根据地点准确判定区域ID，不可返回null**
- 依据出发/到达地点中的城市/机场/地标自动判定区域 ID：
    1: KL/Selangor（Kuala Lumpur、KLIA/1/2、Selangor、Putrajaya、Subang、Sunway、Shah Alam、Cyberjaya、云顶/吉隆坡 等）
    2: Penang（Penang、PEN、George Town、Bayan Lepas、Butterworth、槟城 等）
    3: Johor（Johor/Johor Bahru、JHB、Senai、Desaru、乐高乐园/新山 等）
    4: Sabah（Kota Kinabalu/KK、BKI、Kundasang、Semporna、Tawau、**斗湖机场/斗湖**、仙本那、亚庇 等）
    5: Singapore（Singapore、Changi、SIN、樟宜/新加坡 等）
    12: Malacca（Melaka、Malacca、马六甲 等）
- **特别注意：斗湖机场MAIN → 4 (Sabah)**
- 如出现多个区域线索，以更具体的机场或城市优先。

【最终输出要求】
- 仅输出符合上述结构的 JSON（无多余解释/无Markdown代码块）。
- 单订单→返回“单个对象”；多订单→返回“多订单对象”。
- 所有定义字段必须出现，即使为 null。
 - 严禁输出未在“字段定义”中列出的任何字段；特别是不要返回 languages_id_array、language、languages、orderIndex、processedAt 等中间/调试字段。
 - 以下字段即使文本未给出也必须显式出现（未知则置为 null）：pickup_date、driving_region_id、customer_email、flight_info、extra_requirement。

订单信息：`;

            // 通用提示词（无渠道特征时使用）
            this.genericPrompt = `
通用处理规则：
- 使用标准的订单解析逻辑
- 支持多语言和多渠道格式
- 采用通用的价格计算方式
- 标准车型推荐算法
`;

            this.logger.log('提示词构建器已初始化', 'info');
        }

        /**
         * 构建提示词
         * @param {string} input - 输入内容
         * @param {object} channelResult - 渠道检测结果
         * @param {object} options - 构建选项
         * @returns {Promise<string>} 构建后的提示词
         */
        async buildPrompt(input, channelResult, options = {}) {
            try {
                this.logger.log('开始构建提示词', 'info', {
                    channel: channelResult.channel,
                    inputLength: input.length,
                    autoTriggered: options.autoTriggered || false,
                    sourceField: options.sourceField || 'unknown'
                });

                let prompt = this.basePromptTemplate;

                // 无渠道特征 → 使用通用提示词
                if (!channelResult.channel) {
                    prompt += this.genericPrompt;
                    prompt += `\n\n${input}`;

                    this.logger.log('使用通用提示词', 'info');
                    return prompt;
                }

                // 有渠道特征 → 调取渠道策略文件的字段片段提示词
                this.logger.log('🔧 [调试] 获取渠道策略', 'debug', { channel: channelResult.channel });
                const strategy = this.getChannelStrategy(channelResult.channel);
                if (strategy) {
                    this.logger.log('🔧 [调试] 渠道策略找到，获取字段片段', 'debug', { 
                        strategyType: strategy.name || 'unknown',
                        hasGetFieldPromptSnippets: typeof strategy.getFieldPromptSnippets === 'function'
                    });
                    const fieldSnippets = this.getFieldPromptSnippets(strategy, options);
                    this.logger.log('🔧 [调试] 字段片段获取完成', 'debug', { 
                        snippetCount: Object.keys(fieldSnippets || {}).length,
                        snippetKeys: Object.keys(fieldSnippets || {}).join(', ')
                    });
                    prompt = this.combinePrompts(prompt, fieldSnippets, channelResult.channel, options);
                } else {
                    this.logger.log('未找到对应的渠道策略，使用通用提示词', 'warning', {
                        channel: channelResult.channel
                    });
                    prompt += this.genericPrompt;
                }

                prompt += `\n\n${input}`;

                this.logger.log('提示词构建完成', 'success', {
                    channel: channelResult.channel,
                    promptLength: prompt.length,
                    autoTriggered: options.autoTriggered || false
                });

                return prompt;

            } catch (error) {
                this.logger.log('提示词构建失败', 'error', { error: error.message });
                // 降级方案：返回基础提示词
                return this.basePromptTemplate + `\n\n${input}`;
            }
        }

    // 已移除：本地驾驶区域推断逻辑，统一交由 Gemini 通过提示词执行

        /**
         * 获取渠道策略
         * @param {string} channel - 渠道名称
         * @returns {object|null} 策略对象
         */
        getChannelStrategy(channel) {
            try {
                const normalizedChannel = channel.toLowerCase();
                
                // 获取Fliggy策略
                if (normalizedChannel.includes('fliggy') || normalizedChannel === 'fliggy') {
                    if (typeof window.FliggyOTAStrategy !== 'undefined') {
                        return window.FliggyOTAStrategy;
                    }
                }
                
                // 获取JingGe策略
                if (normalizedChannel.includes('jingge') || normalizedChannel === 'jingge') {
                    if (typeof window.JingGeOTAStrategy !== 'undefined') {
                        return window.JingGeOTAStrategy;
                    }
                }

                // 其他渠道策略可以在这里添加
                this.logger.log('未找到对应的渠道策略', 'warning', { channel });
                return null;

            } catch (error) {
                this.logger.log('获取渠道策略失败', 'error', { channel, error: error.message });
                return null;
            }
        }

        /**
         * 获取字段提示词片段
         * @param {object} strategy - 策略对象
         * @param {object} options - 选项
         * @returns {object} 字段提示词片段
         */
        getFieldPromptSnippets(strategy, options = {}) {
            try {
                if (strategy && typeof strategy.getFieldPromptSnippets === 'function') {
                    const snippets = strategy.getFieldPromptSnippets(options);
                    
                    this.logger.log('成功获取字段提示词片段', 'success', { 
                        snippetCount: Object.keys(snippets).length 
                    });
                    
                    return snippets;
                } else {
                    this.logger.log('策略对象不包含getFieldPromptSnippets方法', 'warning');
                    return {};
                }
            } catch (error) {
                this.logger.log('获取字段提示词片段失败', 'error', { error: error.message });
                return {};
            }
        }

        /**
         * 组合提示词
         * @param {string} basePrompt - 基础提示词
         * @param {object} fieldSnippets - 字段提示词片段
         * @param {string} channel - 渠道名称
         * @param {object} options - 构建选项
         * @returns {string} 组合后的提示词
         */
        combinePrompts(basePrompt, fieldSnippets, channel, options = {}) {
            try {
                let combinedPrompt = basePrompt;

                // 添加渠道专属说明
                combinedPrompt += `\n\n**${channel}渠道专属处理规则：**\n`;

                // 如果是自动触发，添加特殊说明
                if (options.autoTriggered) {
                    combinedPrompt += `\n**自动检测模式：**\n`;
                    combinedPrompt += `- 检测置信度：${options.confidence || 'N/A'}\n`;
                    combinedPrompt += `- 检测来源字段：${options.sourceField || 'unknown'}\n`;
                    combinedPrompt += `- 请基于检测到的渠道特征进行精确解析\n\n`;
                }

                // 添加字段级提示词片段
                if (fieldSnippets && Object.keys(fieldSnippets).length > 0) {
                    const fieldInstructions = Object.entries(fieldSnippets)
                        .map(([field, instruction], index) => `${index + 1}. [${field}] ${instruction}`)
                        .join('\n');

                    combinedPrompt += fieldInstructions;
                    combinedPrompt += '\n';
                }

                // 添加渠道标识
                combinedPrompt += `\n**当前处理渠道：${channel}**\n`;
                combinedPrompt += '请严格按照该渠道的处理规则进行解析。\n';

                // 如果是自动触发，添加额外的精确度要求
                if (options.autoTriggered) {
                    combinedPrompt += '\n**自动分析要求：**\n';
                    combinedPrompt += '- 基于检测到的渠道特征，提供高精度的字段提取\n';
                    combinedPrompt += '- 优先使用渠道专属的解析规则和格式\n';
                    combinedPrompt += '- 确保价格计算符合该渠道的特定规则\n';
                }

                return combinedPrompt;

            } catch (error) {
                this.logger.log('组合提示词失败', 'error', { error: error.message });
                return basePrompt;
            }
        }

        /**
         * 获取支持的渠道列表
         * @returns {array} 支持的渠道列表
         */
        getSupportedChannels() {
            const supportedChannels = [];

            // 检查Fliggy策略是否可用
            if (typeof window.FliggyOTAStrategy !== 'undefined') {
                supportedChannels.push({
                    name: 'fliggy',
                    displayName: 'Fliggy',
                    strategy: 'FliggyOTAStrategy'
                });
            }

            // 检查JingGe策略是否可用
            if (typeof window.JingGeOTAStrategy !== 'undefined') {
                supportedChannels.push({
                    name: 'jingge',
                    displayName: 'JingGe',
                    strategy: 'JingGeOTAStrategy'
                });
            }

            return supportedChannels;
        }

        /**
         * 验证策略文件完整性
         * @returns {object} 验证结果
         */
        validateStrategies() {
            const validation = {
                valid: true,
                errors: [],
                warnings: [],
                supportedChannels: []
            };

            try {
                const supportedChannels = this.getSupportedChannels();
                validation.supportedChannels = supportedChannels;

                if (supportedChannels.length === 0) {
                    validation.valid = false;
                    validation.errors.push('没有找到任何可用的渠道策略文件');
                }

                // 验证每个策略的接口完整性
                for (const channel of supportedChannels) {
                    const strategy = this.getChannelStrategy(channel.name);
                    if (strategy) {
                        if (typeof strategy.getFieldPromptSnippets !== 'function') {
                            validation.warnings.push(`${channel.displayName}策略缺少getFieldPromptSnippets方法`);
                        }
                        if (typeof strategy.getChannelName !== 'function') {
                            validation.warnings.push(`${channel.displayName}策略缺少getChannelName方法`);
                        }
                    }
                }

                this.logger.log('策略文件验证完成', 'info', validation);
                return validation;

            } catch (error) {
                validation.valid = false;
                validation.errors.push(`策略验证失败: ${error.message}`);
                this.logger.log('策略文件验证失败', 'error', { error: error.message });
                return validation;
            }
        }

        /**
         * 获取日志服务
         */
        getLogger() {
            return window.OTA?.logger || window.logger || console;
        }
    }

    // 创建全局实例
    const promptBuilder = new PromptBuilder();

    // 导出到全局作用域
    window.PromptBuilder = PromptBuilder;
    window.OTA.PromptBuilder = PromptBuilder;
    window.OTA.promptBuilder = promptBuilder;

    console.log('✅ PromptBuilder (子层实现) 已加载');

})();
